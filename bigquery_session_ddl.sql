-- ManciniMES Session Continuity Tables
-- Enhanced session tracking for no-trading-day handling

-- Session status tracking table
CREATE OR REPLACE TABLE `mes_mancini.session_status` (
  session_date DATE NOT NULL,
  run_id STRING,
  
  -- Session classification
  session_status STRING NOT NULL, -- NO_DATA, NO_TRADES, INCOMPLETE, ACTIVE
  
  -- File tracking
  files_expected INTEGER DEFAULT 5,
  files_found INTEGER DEFAULT 0,
  files_missing ARRAY<STRING>,
  
  -- Content verification
  content_hash STRING, -- For idempotency
  processing_status STRING DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETE, ERROR
  
  -- Metadata
  strategy_version STRING,
  mode STRING, -- Live, Playback, Replay
  account STRING,
  config_hash STRING,
  
  -- Timestamps
  folder_created_at TIMESTAMP,
  processed_at TIMESTAMP,
  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  
  -- Error tracking
  error_message STRING,
  retry_count INTEGER DEFAULT 0
)
PARTITION BY session_date
CLUSTER BY session_status, processing_status;

-- Daily session summary with continuity
CREATE OR REPLACE TABLE `mes_mancini.daily_session_summary` (
  session_date DATE NOT NULL,
  
  -- Trading activity
  has_strategy_run BOOLEAN DEFAULT FALSE,
  has_trades BOOLEAN DEFAULT FALSE,
  trade_count INTEGER DEFAULT 0,
  
  -- Performance summary
  total_pnl NUMERIC(10,2) DEFAULT 0,
  avg_r_multiple NUMERIC(6,3),
  best_trade NUMERIC(10,2),
  worst_trade NUMERIC(10,2),
  
  -- Exit breakdown
  target_exits INTEGER DEFAULT 0,
  trail_exits INTEGER DEFAULT 0,
  stop_exits INTEGER DEFAULT 0,
  
  -- Risk metrics
  avg_risk_per_trade NUMERIC(10,2),
  max_risk_taken NUMERIC(10,2),
  blocked_entries INTEGER DEFAULT 0,
  
  -- MFE trail performance
  trail_armed_count INTEGER DEFAULT 0,
  avg_mfe_capture NUMERIC(5,2),
  runner_contribution_pct NUMERIC(5,2),
  
  -- Session metadata
  session_duration_hours NUMERIC(5,2),
  bars_processed INTEGER,
  ticks_processed INTEGER,
  
  -- Data quality
  data_completeness_pct NUMERIC(5,2) DEFAULT 0,
  processing_errors INTEGER DEFAULT 0,
  
  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY session_date;

-- Enhanced session continuity view
CREATE OR REPLACE VIEW `mes_mancini.session_continuity_v2` AS
WITH date_spine AS (
  SELECT date_val as session_date
  FROM UNNEST(GENERATE_DATE_ARRAY(
    DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY), 
    CURRENT_DATE()
  )) as date_val
  WHERE EXTRACT(DAYOFWEEK FROM date_val) BETWEEN 2 AND 6 -- Mon-Fri only
),
session_data AS (
  SELECT 
    s.session_date,
    s.session_status,
    s.files_found,
    s.files_expected,
    s.processing_status,
    ds.has_trades,
    ds.trade_count,
    ds.total_pnl,
    ds.data_completeness_pct
  FROM date_spine d
  LEFT JOIN `mes_mancini.session_status` s USING (session_date)
  LEFT JOIN `mes_mancini.daily_session_summary` ds USING (session_date)
)
SELECT 
  session_date,
  
  -- Unified session status
  CASE 
    WHEN session_status IS NULL THEN 'NO_DATA'
    WHEN session_status = 'NO_DATA' THEN 'NO_DATA'
    WHEN session_status = 'ACTIVE' AND COALESCE(has_trades, FALSE) THEN 'ACTIVE'
    WHEN session_status = 'ACTIVE' AND NOT COALESCE(has_trades, FALSE) THEN 'NO_TRADES'
    WHEN session_status = 'INCOMPLETE' THEN 'INCOMPLETE'
    ELSE 'UNKNOWN'
  END as unified_status,
  
  -- Data quality indicators
  COALESCE(files_found, 0) as files_found,
  COALESCE(files_expected, 5) as files_expected,
  COALESCE(data_completeness_pct, 0) as data_completeness_pct,
  
  -- Trading metrics
  COALESCE(trade_count, 0) as trade_count,
  COALESCE(total_pnl, 0) as total_pnl,
  
  -- Status indicators for dashboards
  processing_status,
  
  -- Streak calculations
  LAG(session_date) OVER (ORDER BY session_date) as prev_session_date,
  LEAD(session_date) OVER (ORDER BY session_date) as next_session_date
  
FROM session_data
ORDER BY session_date DESC;

-- Trading streak analysis
CREATE OR REPLACE VIEW `mes_mancini.trading_streaks` AS
WITH session_flags AS (
  SELECT 
    session_date,
    unified_status,
    trade_count,
    total_pnl,
    CASE WHEN unified_status = 'ACTIVE' THEN 1 ELSE 0 END as is_active,
    CASE WHEN unified_status = 'NO_DATA' THEN 1 ELSE 0 END as is_no_data,
    CASE WHEN total_pnl > 0 THEN 1 ELSE 0 END as is_profitable
  FROM `mes_mancini.session_continuity_v2`
),
streak_groups AS (
  SELECT 
    *,
    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) 
      OVER (ORDER BY session_date ROWS UNBOUNDED PRECEDING) as active_group,
    SUM(CASE WHEN is_no_data = 0 THEN 1 ELSE 0 END) 
      OVER (ORDER BY session_date ROWS UNBOUNDED PRECEDING) as data_group,
    SUM(CASE WHEN is_profitable = 0 THEN 1 ELSE 0 END) 
      OVER (ORDER BY session_date ROWS UNBOUNDED PRECEDING) as profit_group
  FROM session_flags
)
SELECT 
  session_date,
  unified_status,
  trade_count,
  total_pnl,
  
  -- Current streaks
  ROW_NUMBER() OVER (PARTITION BY active_group ORDER BY session_date DESC) as active_streak,
  ROW_NUMBER() OVER (PARTITION BY data_group ORDER BY session_date DESC) as data_streak,
  ROW_NUMBER() OVER (PARTITION BY profit_group ORDER BY session_date DESC) as profit_streak
  
FROM streak_groups
WHERE session_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
ORDER BY session_date DESC;

-- Data quality monitoring
CREATE OR REPLACE VIEW `mes_mancini.data_quality_monitor` AS
SELECT 
  DATE_TRUNC(session_date, WEEK) as week_start,
  
  -- Session counts by status
  COUNTIF(unified_status = 'ACTIVE') as active_sessions,
  COUNTIF(unified_status = 'NO_TRADES') as no_trade_sessions,
  COUNTIF(unified_status = 'NO_DATA') as no_data_sessions,
  COUNTIF(unified_status = 'INCOMPLETE') as incomplete_sessions,
  
  -- Data completeness
  AVG(data_completeness_pct) as avg_completeness,
  MIN(data_completeness_pct) as min_completeness,
  
  -- Trading activity
  SUM(trade_count) as total_trades,
  SUM(total_pnl) as total_pnl,
  
  -- Data quality score (0-100)
  ROUND(
    (COUNTIF(unified_status IN ('ACTIVE', 'NO_TRADES')) / COUNT(*)) * 100, 1
  ) as data_quality_score
  
FROM `mes_mancini.session_continuity_v2`
WHERE session_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 WEEK)
GROUP BY week_start
ORDER BY week_start DESC;
