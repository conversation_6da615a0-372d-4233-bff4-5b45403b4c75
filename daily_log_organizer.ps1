# ManciniMES Daily Log Organizer v2.0
# Production-ready log organization with comprehensive no-data handling
# Integrates with automated analytics pipeline

param(
    [string]$SourcePath = "$env:USERPROFILE\Documents\NinjaTrader 8\logs\ManciniMES",
    [string]$DestinationPath = "$env:USERPROFILE\Google Drive\MES_Mancini",
    [switch]$Force = $false,
    [switch]$DryRun = $false,
    [switch]$ProcessYesterday = $true
)

# Configuration
$LogTypes = @('Trades_', 'Risk_', 'Debug_', 'MFETrail_', 'Patterns_')
$TradingDays = @('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday')

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

function Get-TradingDate {
    param([DateTime]$Date)
    
    # Get the most recent trading day (Mon-Fri)
    while ($Date.DayOfWeek -notin @('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday')) {
        $Date = $Date.AddDays(-1)
    }
    return $Date
}

function Find-LogFiles {
    param([string]$SourcePath, [DateTime]$TargetDate)
    
    $dateString = $TargetDate.ToString('yyyyMMdd')
    $foundFiles = @{}
    $missingTypes = @()
    
    Write-Log "Looking for log files with date: $dateString"
    
    if (-not (Test-Path $SourcePath)) {
        Write-Log "Source path does not exist: $SourcePath" "ERROR"
        return $foundFiles, $LogTypes
    }
    
    foreach ($logType in $LogTypes) {
        $pattern = "${logType}${dateString}_*.txt"
        $files = Get-ChildItem -Path $SourcePath -Name $pattern -ErrorAction SilentlyContinue
        
        if ($files) {
            # Take the most recent file if multiple exist
            $latestFile = $files | Sort-Object | Select-Object -Last 1
            $foundFiles[$logType.TrimEnd('_')] = Join-Path $SourcePath $latestFile
            Write-Log "Found: $latestFile"
        } else {
            $missingTypes += $logType.TrimEnd('_')
            Write-Log "Missing: $pattern" "WARN"
        }
    }
    
    return $foundFiles, $missingTypes
}

function New-DatedFolder {
    param([string]$DestinationPath, [DateTime]$Date)

    $dateString = $Date.ToString('yyyyMMdd')
    $folderPath = Join-Path $DestinationPath $dateString

    if (-not $DryRun) {
        if (-not (Test-Path $folderPath)) {
            New-Item -ItemType Directory -Path $folderPath -Force | Out-Null
            Write-Log "Created folder: $folderPath"
        } else {
            Write-Log "Folder already exists: $folderPath"
        }
    } else {
        Write-Log "[DRY RUN] Would create folder: $folderPath"
    }

    return $folderPath
}

function Get-SessionContentHash {
    param([hashtable]$FoundFiles)

    $combinedContent = ""
    foreach ($file in $FoundFiles.Values | Sort-Object) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw -ErrorAction SilentlyContinue
            $combinedContent += $content
        }
    }

    $hash = [System.Security.Cryptography.SHA256]::Create()
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($combinedContent)
    $hashBytes = $hash.ComputeHash($bytes)
    $hashString = [System.BitConverter]::ToString($hashBytes).Replace('-', '').Substring(0, 16)

    return $hashString.ToLower()
}

function Copy-LogFiles {
    param([hashtable]$FoundFiles, [string]$DestinationFolder)
    
    $copiedCount = 0
    
    foreach ($logType in $FoundFiles.Keys) {
        $sourceFile = $FoundFiles[$logType]
        $fileName = Split-Path $sourceFile -Leaf
        $destFile = Join-Path $DestinationFolder $fileName
        
        if (-not $DryRun) {
            try {
                Copy-Item -Path $sourceFile -Destination $destFile -Force
                Write-Log "Copied: $fileName"
                $copiedCount++
            } catch {
                Write-Log "Failed to copy $fileName : $($_.Exception.Message)" "ERROR"
            }
        } else {
            Write-Log "[DRY RUN] Would copy: $fileName"
            $copiedCount++
        }
    }
    
    return $copiedCount
}

function New-SessionMarker {
    param([string]$DestinationFolder, [array]$MissingTypes, [DateTime]$Date, [hashtable]$FoundFiles)

    $markerFile = Join-Path $DestinationFolder "session_status.json"

    # Determine session status
    $sessionStatus = if ($MissingTypes.Count -eq $LogTypes.Count) {
        'NO_DATA'  # No strategy run
    } elseif ($MissingTypes.Count -gt 0) {
        'INCOMPLETE'  # Partial data
    } elseif ($FoundFiles.Count -gt 0) {
        'ACTIVE'  # Complete data
    } else {
        'NO_TRADES'  # Strategy ran but no trades
    }

    $markerData = @{
        session_date = $Date.ToString('yyyy-MM-dd')
        session_status = $sessionStatus
        files_found = @($FoundFiles.Keys)
        files_missing = $MissingTypes
        total_expected = $LogTypes.Count
        total_found = $FoundFiles.Count
        created_at = (Get-Date).ToString('yyyy-MM-dd HH:mm:ss.fff')
        pipeline_version = '2.0'
    } | ConvertTo-Json -Depth 3

    if (-not $DryRun) {
        $markerData | Out-File -FilePath $markerFile -Encoding UTF8
        Write-Log "Created session marker: $sessionStatus"
    } else {
        Write-Log "[DRY RUN] Would create session marker: $sessionStatus"
    }
}

function Invoke-TradingDayProcessing {
    param([DateTime]$Date)

    Write-Log "Processing trading day: $($Date.ToString('yyyy-MM-dd'))"

    # Find log files for this date
    $foundFiles, $missingTypes = Find-LogFiles -SourcePath $SourcePath -TargetDate $Date

    # Create destination folder
    $destFolder = New-DatedFolder -DestinationPath $DestinationPath -Date $Date

    # Always create session status marker
    New-SessionMarker -DestinationFolder $destFolder -MissingTypes $missingTypes -Date $Date -FoundFiles $foundFiles

    if ($foundFiles.Count -gt 0) {
        # Copy found files
        $copiedCount = Copy-LogFiles -FoundFiles $foundFiles -DestinationFolder $destFolder
        Write-Log "Copied $copiedCount files for $($Date.ToString('yyyy-MM-dd'))"

        # Calculate file hash for idempotency
        $contentHash = Get-SessionContentHash -FoundFiles $foundFiles
        $hashFile = Join-Path $destFolder "content_hash.txt"
        if (-not $DryRun) {
            $contentHash | Out-File -FilePath $hashFile -Encoding UTF8
        }

        Write-Log "Session hash: $contentHash"
    } else {
        Write-Log "No log files found for $($Date.ToString('yyyy-MM-dd'))"
    }

    return @{
        Date = $Date
        FilesFound = $foundFiles.Count
        FilesMissing = $missingTypes.Count
        Status = if ($foundFiles.Count -eq 0) { 'NO_DATA' } elseif ($missingTypes.Count -gt 0) { 'INCOMPLETE' } else { 'COMPLETE' }
    }
}

# Main execution
try {
    Write-Log "Starting ManciniMES Daily Log Organizer"
    Write-Log "Source: $SourcePath"
    Write-Log "Destination: $DestinationPath"
    
    if ($DryRun) {
        Write-Log "DRY RUN MODE - No files will be modified"
    }
    
    # Ensure destination path exists
    if (-not $DryRun -and -not (Test-Path $DestinationPath)) {
        New-Item -ItemType Directory -Path $DestinationPath -Force | Out-Null
        Write-Log "Created destination directory: $DestinationPath"
    }
    
    # Process today (or most recent trading day)
    $today = Get-TradingDate -Date (Get-Date)
    $todayResult = Invoke-TradingDayProcessing -Date $today

    # Optionally process yesterday if it's a different trading day
    if ($ProcessYesterday) {
        $yesterday = Get-TradingDate -Date (Get-Date).AddDays(-1)
        if ($yesterday -ne $today) {
            Write-Log "Also processing previous trading day"
            $yesterdayResult = Invoke-TradingDayProcessing -Date $yesterday
        }
    }

    # Summary report
    Write-Log "=== PROCESSING SUMMARY ==="
    Write-Log "Today ($($today.ToString('yyyy-MM-dd'))): $($todayResult.Status) - $($todayResult.FilesFound) files found, $($todayResult.FilesMissing) missing"
    if ($yesterdayResult) {
        Write-Log "Yesterday ($($yesterday.ToString('yyyy-MM-dd'))): $($yesterdayResult.Status) - $($yesterdayResult.FilesFound) files found, $($yesterdayResult.FilesMissing) missing"
    }

    Write-Log "Daily log organization completed successfully"
    
} catch {
    Write-Log "Error during execution: $($_.Exception.Message)" "ERROR"
    exit 1
}

# Schedule this script to run at 4:00 PM ET daily:
# schtasks /create /tn "ManciniMES Log Organizer" /tr "powershell.exe -File 'C:\path\to\daily_log_organizer.ps1'" /sc daily /st 16:00
