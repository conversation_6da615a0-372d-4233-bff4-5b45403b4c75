# ManciniMES Log Analysis Pipeline - Production Configuration
# Project: mmancinimes-data
# Environment: Production
# Updated: 2025-09-02

# Google Cloud Configuration
gcp:
  project_id: "mmancinimes-data"
  region: "us-central1"
  zone: "us-central1-a"
  
# LangExtract Configuration for Gemini
langextract:
  api_key: "AIzaSyC_VaB_tzbRFSqq0n63ix1lOFvepcj_saw"
  model: "gemini-1.5-pro"
  temperature: 0.1
  max_tokens: 8192
  
# Notification Configuration
notifications:
  email: "<EMAIL>"
  slack_webhook: null  # Optional: Add Slack webhook if needed
  
# Cloud Run Configuration
cloud_run:
  service_name: "mancinimes-log-processor"
  memory: "2Gi"
  cpu: "2"
  max_instances: 10
  timeout: "3600s"
  
# BigQuery Configuration
bigquery:
  dataset_id: "mancinimes_analytics"
  location: "US"
  tables:
    trades: "trade_performance"
    executions: "order_executions"
    risk: "risk_management"
    mfe: "mfe_trail_analysis"
    patterns: "pattern_detection"
    performance: "system_performance"
    
# Cloud Storage Configuration
storage:
  bucket_name: "mancinimes-logs"
  processed_folder: "processed"
  archive_folder: "archive"
  temp_folder: "temp"
  
# Processing Configuration
processing:
  batch_size: 100
  parallel_workers: 4
  retry_attempts: 3
  timeout_seconds: 300
  
# Log Analysis Configuration
log_analysis:
  # Trade Log Analysis
  trade_patterns:
    - entry_analysis
    - exit_analysis
    - mfe_performance
    - risk_metrics
    - correlation_tracking
    
  # Execution Log Analysis
  execution_patterns:
    - order_timing
    - fill_analysis
    - latency_metrics
    - broker_performance
    - retry_analysis
    
  # Risk Log Analysis
  risk_patterns:
    - validation_decisions
    - emergency_actions
    - position_sizing
    - volatility_analysis
    - compliance_tracking
    
  # MFE Trail Analysis
  mfe_patterns:
    - trail_arming
    - capture_rates
    - exit_performance
    - threshold_analysis
    - sequence_tracking
    
  # Pattern Detection Analysis
  pattern_analysis:
    - breakdown_detection
    - reclaim_validation
    - support_levels
    - entry_conditions
    - block_reasons
    
  # Performance Analysis
  performance_metrics:
    - processing_times
    - memory_usage
    - debounce_effectiveness
    - io_performance
    - error_rates

# Data Retention
retention:
  raw_logs: "90d"
  processed_data: "2y"
  aggregated_data: "5y"
  
# Monitoring & Alerting
monitoring:
  health_check_interval: "5m"
  error_threshold: 5
  latency_threshold: "30s"
  
  alerts:
    - name: "processing_failures"
      condition: "error_rate > 10%"
      severity: "high"
      
    - name: "high_latency"
      condition: "avg_latency > 60s"
      severity: "medium"
      
    - name: "data_quality"
      condition: "missing_trades > 0"
      severity: "high"

# Dashboard Configuration
dashboards:
  looker_studio:
    - name: "Trade Performance"
      template: "trade_dashboard"
      
    - name: "Risk Management"
      template: "risk_dashboard"
      
    - name: "System Health"
      template: "performance_dashboard"
      
    - name: "MFE Trail Analysis"
      template: "mfe_dashboard"

# Automation Configuration
automation:
  schedule: "0 6 * * *"  # Daily at 6 AM ET
  timezone: "America/New_York"
  source_path: "C:\\Users\\<USER>\\Documents\\NinjaTrader 8\\logs\\ManciniMES"
  
# Security Configuration
security:
  encryption_at_rest: true
  encryption_in_transit: true
  access_control: "IAM"
  audit_logging: true
