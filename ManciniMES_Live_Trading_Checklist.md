# ManciniMESStrategy v2.4 - Live Trading Checklist

## 🎯 Pre-Trading Setup

### **1. Strategy Configuration**
- [ ] **Version Verification**: Confirm running v2.4.0 with critical production fixes
- [ ] **MaxRiskPerTrade**: Set appropriate dollar risk (typically $100-$300)
- [ ] **StopDistanceATR**: Configure ATR multiplier (typically 1.5-2.0x)
- [ ] **MaxDailyTrades**: Set daily limit (typically 1-3 trades)
- [ ] **Support Levels**: Configure S1/S2/S3 levels for the session
- [ ] **Trading Hours**: Verify ETH (6PM-3:50PM) or RTH (9:30AM-3:50PM) setting

### **2. Risk Management Validation**
- [ ] **Position Sizing**: Verify calculated contract sizes are reasonable
- [ ] **Account Balance**: Ensure sufficient margin for maximum position
- [ ] **Daily Loss Limit**: Set personal daily loss limit
- [ ] **News Events**: Check economic calendar for high-impact events

### **3. Critical Production Fixes Verification (v2.4)**
- [ ] **ETH Session Protection**: Verify bulletproof close only triggers during RTH (9:30 AM - 4:00 PM ET)
- [ ] **Atomic Finalization**: Confirm global halt flags prevent orphaned orders
- [ ] **Immediate Brackets**: Verify brackets placed instantly on entry fills (not 5 minutes later)
- [ ] **Robust Cancellation**: Check that ALL order states can be cancelled (including Initialized)
- [ ] **Universal Guards**: Confirm no order submissions when position flat or finalizing
- [ ] **System Validation**: Enable DebugMode initially to see integrity validation reports

### **4. Operational Excellence Verification (v2.3)**
- [ ] **Run Headers**: Verify professional headers appear in all log files
- [ ] **Version Tracking**: Confirm v2.4.0 appears in startup logs
- [ ] **Configuration Hash**: Note config hash for correlation
- [ ] **Account Masking**: Verify account ID is properly masked
- [ ] **Performance Counters**: Confirm EOD metrics are being tracked

## 🚀 Live Trading Execution

### **5. Session Start**
- [ ] **Log File Check**: Verify all log files are being created
- [ ] **Pattern Detection**: Confirm support levels are being monitored
- [ ] **ATR Calculation**: Verify ATR values are reasonable (typically 10-25 points)
- [ ] **Prior Day Low**: Confirm PDL calculation is accurate
- [ ] **Trading Hours**: Verify session boundaries are correct

### **6. During Trading**
- [ ] **Entry Signals**: Monitor pattern detection logs for breakdown/reclaim
- [ ] **Order Execution**: Verify buy-stop orders are being placed correctly
- [ ] **Risk Monitoring**: Watch for risk guard messages
- [ ] **MFE Trail**: Monitor trail arming and exit triggers
- [ ] **Performance Metrics**: Check tick/bar processing counters

### **7. Position Management (Enhanced v2.4)**
- [ ] **Immediate Bracket Orders**: Verify stop-loss and profit target placed within milliseconds of fill
- [ ] **No Orphaned Orders**: Confirm no working orders remain after position closure
- [ ] **ETH Session Safety**: Verify no premature closes during 20:00 ET entries
- [ ] **MFE Trail Arming**: Confirm trail activates at proper threshold
- [ ] **Break-Even Stops**: Verify runner protection after profit target
- [ ] **Session Close**: Confirm auto-flatten before 3:50 PM ET (RTH only)
- [ ] **Order State Handling**: Verify all order cancellations succeed (no "Initialized" errors)

## 📊 Post-Trading Analysis

### **8. EOD Review**
- [ ] **Performance Report**: Review EOD performance counters
- [ ] **Trade Summary**: Analyze per-trade performance metrics
- [ ] **MFE Capture**: Review MFE capture rates and R-multiples
- [ ] **Log Analysis**: Check for any error or warning messages
- [ ] **Debounce Effectiveness**: Review spam reduction percentages

### **8. Operational Metrics (v2.3)**
- [ ] **I/O Efficiency**: Review debounce savings percentage
- [ ] **Processing Volume**: Check bars/ticks processed
- [ ] **Session Duration**: Verify operational hours tracking
- [ ] **Log File Sizes**: Monitor disk usage estimates
- [ ] **Configuration Stability**: Verify config hash consistency

## 🛡️ Risk Management Checklist

### **9. Daily Risk Controls**
- [ ] **Maximum Risk**: Never exceed configured MaxRiskPerTrade
- [ ] **Daily Trade Limit**: Respect MaxDailyTrades setting
- [ ] **Stop Distance**: Ensure stops never exceed 15 points (Adam's rule)
- [ ] **Position Size**: Verify contract quantities are appropriate
- [ ] **Account Protection**: Monitor account balance and margin

### **10. Emergency Procedures**
- [ ] **Manual Override**: Know how to manually flatten positions
- [ ] **Strategy Disable**: Know how to disable strategy quickly
- [ ] **Broker Contact**: Have broker contact information ready
- [ ] **Log Preservation**: Ensure logs are backed up for analysis
- [ ] **Incident Documentation**: Document any unusual behavior

## 🔧 Technical Checklist

### **11. System Health**
- [ ] **Data Feed**: Verify real-time data is flowing
- [ ] **Order Routing**: Confirm orders are reaching broker
- [ ] **Latency**: Monitor order execution latency
- [ ] **Memory Usage**: Check for memory leaks or excessive usage
- [ ] **Disk Space**: Ensure adequate space for log files

### **12. Compliance & Audit**
- [ ] **Audit Trail**: Verify complete trade lifecycle logging
- [ ] **Correlation IDs**: Confirm all events have proper correlation
- [ ] **Timestamp Accuracy**: Verify all timestamps are consistent
- [ ] **Account Masking**: Ensure sensitive data is properly masked
- [ ] **Version Documentation**: Maintain record of strategy versions used

## 📈 Performance Optimization

### **13. Monitoring**
- [ ] **Execution Quality**: Monitor fill prices vs expected
- [ ] **Slippage Analysis**: Track entry and exit slippage
- [ ] **Trail Effectiveness**: Analyze MFE trail performance
- [ ] **Pattern Accuracy**: Review breakdown/reclaim success rates
- [ ] **Risk-Adjusted Returns**: Calculate Sharpe ratio and R-multiples

### **14. Continuous Improvement**
- [ ] **Parameter Tuning**: Adjust based on market conditions
- [ ] **Performance Review**: Weekly/monthly performance analysis
- [ ] **Strategy Updates**: Stay current with latest versions
- [ ] **Market Adaptation**: Adjust support levels as needed
- [ ] **Documentation Updates**: Keep trading notes and observations

## ✅ Go-Live Approval

**Final checklist before enabling live trading:**

- [ ] All configuration parameters validated
- [ ] Risk management controls tested
- [ ] Operational excellence features verified
- [ ] Emergency procedures understood
- [ ] Performance monitoring in place
- [ ] Compliance requirements met
- [ ] Backup and recovery procedures tested

**Strategy is ready for live trading when ALL items are checked.**