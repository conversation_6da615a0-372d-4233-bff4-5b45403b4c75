# ManciniMES Looker Studio Dashboard Template

## 🎯 Quick Setup

1. **Go to Looker Studio**: https://lookerstudio.google.com/
2. **Create New Report** → **BigQuery** → Select your project
3. **Add Data Sources**:
   - `mes_mancini.daily_coach`
   - `mes_mancini.depth_performance` 
   - `mes_mancini.session_continuity_v2`
   - `mes_mancini.runner_analysis`

## 📊 Dashboard Layout

### **Page 1: Daily Coach Dashboard**

**Data Source**: `mes_mancini.daily_coach`

**Charts to Add**:

1. **Scorecard - Today's Performance**
   - Metric: `net_pnl`
   - Filter: `run_date = TODAY()`
   - Style: Large number, green/red conditional formatting

2. **Scorecard - Today's R-Multiple**
   - Metric: `avg_r_multiple`
   - Filter: `run_date = TODAY()`
   - Style: 2 decimal places

3. **Time Series - Daily PnL Trend**
   - Dimension: `run_date`
   - Metric: `net_pnl`
   - Date Range: Last 30 days
   - Style: Line chart with trend line

4. **Bar Chart - Exit Reason Breakdown**
   - Dimensions: `run_date`
   - Metrics: `trail_exits`, `target_exits`, `stop_exits`
   - Style: Stacked bar chart
   - Date Range: Last 7 days

5. **Table - Recent Trading Sessions**
   - Dimensions: `run_date`, `mode`
   - Metrics: `trades`, `net_pnl`, `avg_r_multiple`, `avg_capture_pct`
   - Sort: `run_date` DESC
   - Rows: 10

### **Page 2: Breakdown Depth Analysis**

**Data Source**: `mes_mancini.depth_performance`

**Charts to Add**:

1. **Bar Chart - Performance by Depth**
   - Dimension: `breakdown_depth`
   - Metrics: `avg_r_multiple`, `net_pnl`
   - Style: Grouped bars

2. **Scatter Plot - Depth vs Win Rate**
   - X-Axis: `breakdown_depth`
   - Y-Axis: `win_rate`
   - Size: `trade_count`
   - Color: `avg_r_multiple`

3. **Table - Depth Statistics**
   - Dimension: `breakdown_depth`
   - Metrics: `trade_count`, `avg_r_multiple`, `net_pnl`, `win_rate`, `avg_capture_pct`
   - Sort: `avg_r_multiple` DESC

### **Page 3: Session Continuity**

**Data Source**: `mes_mancini.session_continuity_v2`

**Charts to Add**:

1. **Time Series - Session Status**
   - Dimension: `session_date`
   - Metric: `trade_count`
   - Breakdown: `unified_status`
   - Style: Area chart with different colors for each status

2. **Pie Chart - Session Status Distribution**
   - Dimension: `unified_status`
   - Metric: Count of records
   - Date Range: Last 30 days

3. **Scorecard - Data Quality**
   - Metric: `AVG(data_completeness_pct)`
   - Date Range: Last 30 days
   - Style: Percentage with gauge

4. **Table - Session Details**
   - Dimensions: `session_date`, `unified_status`
   - Metrics: `files_found`, `files_expected`, `trade_count`, `total_pnl`
   - Sort: `session_date` DESC
   - Rows: 20

### **Page 4: Runner Analysis**

**Data Source**: `mes_mancini.runner_analysis`

**Charts to Add**:

1. **Time Series - Runner Contribution**
   - Dimension: `run_date`
   - Metrics: `target_pnl`, `runner_pnl`
   - Style: Stacked area chart

2. **Scorecard - Runner Contribution %**
   - Metric: `AVG(runner_contribution_pct)`
   - Date Range: Last 30 days
   - Style: Percentage

3. **Bar Chart - Trail Effectiveness**
   - Dimension: `run_date`
   - Metrics: `avg_trail_capture`, `trail_arm_rate`
   - Style: Grouped bars
   - Date Range: Last 14 days

## 🎨 Styling Recommendations

### **Color Scheme**:
- **Profitable**: #4CAF50 (Green)
- **Loss**: #F44336 (Red)
- **Neutral**: #2196F3 (Blue)
- **Warning**: #FF9800 (Orange)

### **Conditional Formatting**:
- **PnL**: Green for positive, red for negative
- **R-Multiple**: Green > 1.0, red < 0.5, yellow 0.5-1.0
- **Session Status**: 
  - ACTIVE: Green
  - NO_TRADES: Yellow
  - NO_DATA: Gray
  - INCOMPLETE: Orange
  - ERROR: Red

### **Number Formats**:
- **Currency**: $#,##0.00
- **Percentages**: #0.0%
- **R-Multiples**: #0.00
- **Dates**: MMM dd, yyyy

## 🔧 Advanced Features

### **Filters to Add**:
1. **Date Range Picker** (all pages)
2. **Mode Filter** (Live/Playback/Replay)
3. **Account Filter** (if multiple accounts)

### **Calculated Fields**:

1. **Win Rate**:
   ```sql
   COUNTIF(net_pnl > 0) / COUNT(*)
   ```

2. **Profit Factor**:
   ```sql
   SUM(CASE WHEN net_pnl > 0 THEN net_pnl ELSE 0 END) / 
   ABS(SUM(CASE WHEN net_pnl < 0 THEN net_pnl ELSE 0 END))
   ```

3. **Average Trade Duration**:
   ```sql
   AVG(avg_duration_min)
   ```

### **Interactive Features**:
- **Drill-down**: Click on date to see individual trades
- **Cross-filtering**: Select breakdown depth to filter other charts
- **Hover tooltips**: Show additional metrics on hover

## 📱 Mobile Optimization

- Use responsive layout
- Prioritize key metrics for mobile view
- Simplify charts for small screens
- Use large, readable fonts

## 🔄 Refresh Settings

- **Data Freshness**: Set to refresh every 15 minutes during market hours
- **Cache Duration**: 5 minutes for real-time feel
- **Auto-refresh**: Enable for live monitoring

## 📈 Usage Tips

1. **Daily Review**: Check Page 1 each morning for overnight results
2. **Weekly Analysis**: Use Page 2 to optimize breakdown depth strategy
3. **Data Quality**: Monitor Page 3 for any processing issues
4. **Runner Optimization**: Use Page 4 to improve trail settings

## 🎯 Key Metrics to Watch

- **Daily PnL Trend**: Consistent profitability
- **R-Multiple Average**: Target > 1.0
- **MFE Capture Rate**: Target > 70%
- **Data Completeness**: Target > 95%
- **Runner Contribution**: Target > 30% of profits

## 📅 Holiday Handling

**Note**: The session continuity view uses a Monday-Friday date spine. US market holidays will appear as "NO_DATA" status. For more precise market calendar handling, you can later add a `market_calendar` table with actual trading days and join to that instead of the weekday filter.

This template provides a comprehensive view of your ManciniMES strategy performance with professional-grade analytics and monitoring capabilities.
