#!/usr/bin/env python3
"""
LangExtract Integration Configuration
Supports both Gemini and OpenAI providers for log extraction
"""

import os
import json
import requests
from typing import Dict, List, Any

class LangExtractClient:
    """Unified client for LangExtract with multiple provider support"""
    
    def __init__(self, provider: str = "gemini"):
        self.provider = provider.lower()
        self.api_key = os.environ.get('LANGEXTRACT_API_KEY')
        self.endpoint = os.environ.get('LANGEXTRACT_ENDPOINT', 'https://api.langextract.com')
        
        if not self.api_key:
            raise ValueError("LANGEXTRACT_API_KEY environment variable required")
    
    def extract_from_logs(self, documents: List[Dict], extraction_spec: Dict) -> Dict:
        """Extract structured data from log documents"""
        
        if self.provider == "gemini":
            return self._extract_with_gemini(documents, extraction_spec)
        elif self.provider == "openai":
            return self._extract_with_openai(documents, extraction_spec)
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")
    
    def _extract_with_gemini(self, documents: List[Dict], extraction_spec: Dict) -> Dict:
        """Extract using Gemini provider"""
        
        payload = {
            "provider": "gemini",
            "model": "gemini-1.5-pro",
            "extraction_spec": extraction_spec,
            "documents": documents,
            "options": {
                "include_source_spans": True,
                "batch_size": 50,
                "temperature": 0.1,
                "max_tokens": 8192
            }
        }
        
        return self._make_request(payload)
    
    def _extract_with_openai(self, documents: List[Dict], extraction_spec: Dict) -> Dict:
        """Extract using OpenAI provider"""
        
        payload = {
            "provider": "openai", 
            "model": "gpt-4-turbo",
            "extraction_spec": extraction_spec,
            "documents": documents,
            "options": {
                "include_source_spans": True,
                "batch_size": 30,
                "temperature": 0.0,
                "max_tokens": 4096
            }
        }
        
        return self._make_request(payload)
    
    def _make_request(self, payload: Dict) -> Dict:
        """Make API request to LangExtract with retry logic"""
        import time
        import random

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'ManciniMES-Pipeline/2.0'
        }

        max_retries = 3
        base_delay = 1

        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{self.endpoint}/extract",
                    json=payload,
                    headers=headers,
                    timeout=300  # 5 minute timeout
                )

                response.raise_for_status()
                result = response.json()

                # Log provider and model used for audit
                result['_metadata'] = {
                    'provider': self.provider,
                    'model': payload.get('model'),
                    'attempt': attempt + 1,
                    'timestamp': time.time()
                }

                return result

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise Exception(f"LangExtract API request failed after {max_retries} attempts: {e}")

                # Exponential backoff with jitter
                delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                time.sleep(delay)

# Provider-specific configurations
PROVIDER_CONFIGS = {
    "gemini": {
        "name": "Google Gemini",
        "model": "gemini-1.5-pro",
        "strengths": [
            "Excellent at parsing structured logs",
            "Good with timestamp and numeric extraction", 
            "Cost-effective for high volume",
            "Fast processing"
        ],
        "best_for": "High-volume daily processing with consistent log formats"
    },
    
    "openai": {
        "name": "OpenAI GPT-4",
        "model": "gpt-4-turbo",
        "strengths": [
            "Superior reasoning for complex patterns",
            "Better at handling edge cases",
            "Excellent error recovery",
            "More nuanced understanding"
        ],
        "best_for": "Complex log analysis and pattern recognition"
    }
}

def get_recommended_provider(daily_volume: int, complexity: str = "medium") -> str:
    """Get recommended provider based on usage patterns"""
    
    if daily_volume > 1000 and complexity == "low":
        return "gemini"  # Cost-effective for high volume
    elif complexity == "high":
        return "openai"  # Better for complex analysis
    else:
        return "gemini"  # Default recommendation

def validate_provider_setup(provider: str) -> Dict[str, Any]:
    """Validate provider configuration"""
    
    try:
        client = LangExtractClient(provider)
        
        # Test with minimal payload
        test_documents = [{
            "id": "test",
            "content": "09:50:00.000 [TRADE] Test log entry",
            "metadata": {"file_type": "test"}
        }]
        
        test_spec = {
            "name": "test_extraction",
            "classes": [{
                "name": "TestEntry",
                "fields": [{"name": "timestamp", "type": "string"}]
            }]
        }
        
        # This would make an actual API call - comment out for dry run
        # result = client.extract_from_logs(test_documents, test_spec)
        
        return {
            "status": "valid",
            "provider": provider,
            "config": PROVIDER_CONFIGS.get(provider, {}),
            "message": f"Provider {provider} configuration is valid"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "provider": provider,
            "error": str(e),
            "message": f"Provider {provider} configuration failed: {e}"
        }

# Usage examples
if __name__ == "__main__":
    # Example: Test provider configuration
    provider = os.environ.get('LANGEXTRACT_PROVIDER', 'gemini')
    result = validate_provider_setup(provider)
    print(json.dumps(result, indent=2))
    
    # Example: Get recommendation
    recommendation = get_recommended_provider(daily_volume=100, complexity="medium")
    print(f"Recommended provider: {recommendation}")
    
    # Example: Show provider comparison
    print("\nProvider Comparison:")
    for provider_name, config in PROVIDER_CONFIGS.items():
        print(f"\n{config['name']}:")
        print(f"  Model: {config['model']}")
        print(f"  Best for: {config['best_for']}")
        print("  Strengths:")
        for strength in config['strengths']:
            print(f"    • {strength}")
