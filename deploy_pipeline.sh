#!/bin/bash
# ManciniMES Log Analysis Pipeline - Production Deployment
# Project: mmancinimes-data
# Updated: 2025-09-02

set -e

# Configuration
PROJECT_ID="mmancinimes-data"
REGION="us-central1"
SERVICE_NAME="mes-log-extract"
DATASET_ID="mes_mancini"
ALERT_EMAIL="<EMAIL>"

echo "🚀 Deploying ManciniMES Log Analysis Pipeline"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Email: $ALERT_EMAIL"
echo ""

# Step 1: Set up Google Cloud Project
echo "📋 Step 1: Setting up Google Cloud Project..."
gcloud config set project $PROJECT_ID
gcloud config set compute/region $REGION

# Step 2: Enable required APIs
echo "🔧 Step 2: Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable bigquery.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable eventarc.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable monitoring.googleapis.com

# Step 3: Create BigQuery dataset and tables
echo "📊 Step 3: Setting up BigQuery..."
bq mk --dataset --location=US $PROJECT_ID:$DATASET_ID || echo "Dataset already exists"

# Create tables from DDL
echo "Creating BigQuery tables..."
bq query --use_legacy_sql=false < bigquery_ddl.sql

# Step 4: Create Cloud Storage bucket
echo "🗄️ Step 4: Setting up Cloud Storage..."
gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://$PROJECT_ID-logs || echo "Bucket already exists"

# Step 5: Build and deploy Cloud Run service
echo "☁️ Step 5: Deploying Cloud Run service..."
gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME .

gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
  --platform managed \
  --region $REGION \
  --memory 4Gi \
  --cpu 2 \
  --timeout 900 \
  --concurrency 10 \
  --max-instances 20 \
  --min-instances 0 \
  --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID \
  --set-env-vars LANGEXTRACT_ENDPOINT=https://api.langextract.com \
  --set-env-vars LANGEXTRACT_API_KEY=AIzaSyC_VaB_tzbRFSqq0n63ix1lOFvepcj_saw \
  --allow-unauthenticated

# Step 6: Set up monitoring and alerting
echo "📈 Step 6: Setting up monitoring..."

# Create notification channel for email alerts
gcloud alpha monitoring channels create \
  --display-name="ManciniMES Alerts" \
  --type=email \
  --channel-labels=email_address=$ALERT_EMAIL

# Step 7: Create Eventarc trigger for automated processing
echo "⚡ Step 7: Setting up Eventarc trigger..."
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

gcloud eventarc triggers create mes-log-processor-trigger \
  --location=$REGION \
  --destination-run-service=$SERVICE_NAME \
  --destination-run-region=$REGION \
  --event-filters="type=google.cloud.storage.object.v1.finalized" \
  --event-filters="bucket=$PROJECT_ID-logs" \
  --service-account=$<EMAIL>

# Step 8: Test the deployment
echo "🧪 Step 8: Testing deployment..."
echo "Testing Cloud Run service..."
curl -X POST "$SERVICE_URL/health" -H "Content-Type: application/json"

echo ""
echo "✅ Deployment Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Set up PowerShell automation on your Windows machine"
echo "2. Configure Google Drive sync"
echo "3. Run initial backfill if needed"
echo ""
echo "🔗 Service URL: $SERVICE_URL"
echo "📊 BigQuery Dataset: https://console.cloud.google.com/bigquery?project=$PROJECT_ID&ws=!1m4!1m3!3m2!1s$PROJECT_ID!2s$DATASET_ID"
echo "☁️ Cloud Run Service: https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME?project=$PROJECT_ID"
echo ""
echo "📧 Alerts will be sent to: $ALERT_EMAIL"
