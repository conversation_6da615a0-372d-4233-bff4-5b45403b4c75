#!/bin/bash
# ManciniMES Pipeline Smoke Test
# Validates the complete pipeline deployment

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${REGION:-"us-central1"}
SERVICE_NAME="mes-log-extract"
DATASET_ID="mes_mancini"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}🧪 ManciniMES Pipeline Smoke Test${NC}"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# Check dependencies
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  jq not found - JSON output will be raw${NC}"
    JQ_AVAILABLE=false
else
    JQ_AVAILABLE=true
fi

# Get service URL
SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)" 2>/dev/null || echo "")

if [ -z "$SERVICE_URL" ]; then
    echo -e "${RED}❌ Cloud Run service not found${NC}"
    exit 1
fi

echo "Service URL: $SERVICE_URL"
echo ""

# Test 1: Health Check
echo -e "${YELLOW}Test 1: Health Check${NC}"
if curl -f -s "$SERVICE_URL/health" > /tmp/health_response.json; then
    echo -e "${GREEN}✅ Health check passed${NC}"
    if [ "$JQ_AVAILABLE" = true ]; then
        cat /tmp/health_response.json | jq '.'
    else
        cat /tmp/health_response.json
    fi
else
    echo -e "${RED}❌ Health check failed${NC}"
    exit 1
fi
echo ""

# Test 2: BigQuery Connectivity
echo -e "${YELLOW}Test 2: BigQuery Connectivity${NC}"
if bq query --use_legacy_sql=false "SELECT COUNT(*) as table_count FROM \`$PROJECT_ID.$DATASET_ID.__TABLES__\`" > /tmp/bq_test.txt 2>&1; then
    table_count=$(grep -o '[0-9]\+' /tmp/bq_test.txt | head -1)
    echo -e "${GREEN}✅ BigQuery connectivity verified - $table_count tables found${NC}"
else
    echo -e "${RED}❌ BigQuery connectivity failed${NC}"
    cat /tmp/bq_test.txt
    exit 1
fi
echo ""

# Test 3: Session Status Table
echo -e "${YELLOW}Test 3: Session Status Table Schema${NC}"
if bq show --format=prettyjson "$PROJECT_ID:$DATASET_ID.session_status" > /tmp/session_schema.json 2>&1; then
    echo -e "${GREEN}✅ Session status table exists${NC}"
    echo "Key fields:"
    if [ "$JQ_AVAILABLE" = true ]; then
        jq -r '.schema.fields[] | select(.name | test("session_date|session_status|files_missing")) | "  - \(.name): \(.type)"' /tmp/session_schema.json
    else
        echo "  - session_date, session_status, files_missing (jq not available for details)"
    fi
else
    echo -e "${RED}❌ Session status table missing${NC}"
    exit 1
fi
echo ""

# Test 4: Trades Fact Table
echo -e "${YELLOW}Test 4: Trades Fact Table Schema${NC}"
if bq show --format=prettyjson "$PROJECT_ID:$DATASET_ID.trades_fact" > /tmp/trades_schema.json 2>&1; then
    echo -e "${GREEN}✅ Trades fact table exists${NC}"
    echo "Key fields:"
    if [ "$JQ_AVAILABLE" = true ]; then
        jq -r '.schema.fields[] | select(.name | test("full_trade_id|entry_time|total_pnl|r_multiple")) | "  - \(.name): \(.type)"' /tmp/trades_schema.json
    else
        echo "  - full_trade_id, entry_time, total_pnl, r_multiple (jq not available for details)"
    fi
else
    echo -e "${RED}❌ Trades fact table missing${NC}"
    exit 1
fi
echo ""

# Test 5: Views Exist
echo -e "${YELLOW}Test 5: Dashboard Views${NC}"
views=("daily_coach" "depth_performance" "session_continuity_v2" "runner_analysis")
for view in "${views[@]}"; do
    if bq show "$PROJECT_ID:$DATASET_ID.$view" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ View $view exists${NC}"
    else
        echo -e "${RED}❌ View $view missing${NC}"
        exit 1
    fi
done
echo ""

# Test 6: Process Endpoint (Dry Run)
echo -e "${YELLOW}Test 6: Process Endpoint (Dry Run)${NC}"
test_payload='{"folder_path": "gs://test-bucket/MES_Mancini/20250831", "dry_run": true}'
if curl -f -s -X POST "$SERVICE_URL/process" \
    -H "Content-Type: application/json" \
    -d "$test_payload" > /tmp/process_response.json 2>&1; then
    echo -e "${GREEN}✅ Process endpoint accessible${NC}"
    if [ "$JQ_AVAILABLE" = true ]; then
        cat /tmp/process_response.json | jq '.'
    else
        cat /tmp/process_response.json
    fi
else
    echo -e "${YELLOW}⚠️  Process endpoint test failed (expected - no test data)${NC}"
    echo "Response:"
    cat /tmp/process_response.json
fi
echo ""

# Test 7: Secret Manager Access
echo -e "${YELLOW}Test 7: Secret Manager Access${NC}"
if gcloud secrets describe langextract-api-key >/dev/null 2>&1; then
    echo -e "${GREEN}✅ LangExtract API key secret exists${NC}"
else
    echo -e "${RED}❌ LangExtract API key secret missing${NC}"
    exit 1
fi
echo ""

# Test 8: Service Account Permissions
echo -e "${YELLOW}Test 8: Service Account Permissions${NC}"
SA_EMAIL="mes-pipeline-sa@$PROJECT_ID.iam.gserviceaccount.com"
if gcloud iam service-accounts describe "$SA_EMAIL" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Service account exists: $SA_EMAIL${NC}"
    
    # Check BigQuery dataset permissions
    if [ "$JQ_AVAILABLE" = true ]; then
        if bq show --format=json "$PROJECT_ID:$DATASET_ID" | jq -r '.access[] | select(.userByEmail == "'$SA_EMAIL'") | .role' | grep -q "WRITER\|OWNER"; then
            echo -e "${GREEN}✅ Service account has BigQuery dataset access${NC}"
        else
            echo -e "${YELLOW}⚠️  Service account BigQuery permissions unclear${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Cannot check BigQuery permissions (jq not available)${NC}"
    fi
else
    echo -e "${RED}❌ Service account missing${NC}"
    exit 1
fi
echo ""

# Test 9: Monitoring Setup
echo -e "${YELLOW}Test 9: Monitoring Setup${NC}"
if gcloud logging metrics describe mes_processing_errors >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Error monitoring metric exists${NC}"
else
    echo -e "${YELLOW}⚠️  Error monitoring metric missing${NC}"
fi

if gcloud logging metrics describe mes_processing_success >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Success monitoring metric exists${NC}"
else
    echo -e "${YELLOW}⚠️  Success monitoring metric missing${NC}"
fi
echo ""

# Test 10: Sample Data Query
echo -e "${YELLOW}Test 10: Sample Data Queries${NC}"
echo "Session continuity (last 7 days):"
bq query --use_legacy_sql=false --max_rows=5 "
SELECT 
  session_date,
  unified_status,
  trade_count,
  total_pnl
FROM \`$PROJECT_ID.$DATASET_ID.session_continuity_v2\`
WHERE session_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
ORDER BY session_date DESC
LIMIT 5
" 2>/dev/null || echo "No data yet (expected for new deployment)"

echo ""
echo "Daily coach view:"
bq query --use_legacy_sql=false --max_rows=3 "
SELECT 
  run_date,
  trades,
  net_pnl,
  avg_r_multiple
FROM \`$PROJECT_ID.$DATASET_ID.daily_coach\`
ORDER BY run_date DESC
LIMIT 3
" 2>/dev/null || echo "No data yet (expected for new deployment)"

echo ""

# Summary
echo -e "${GREEN}🎉 SMOKE TEST SUMMARY${NC}"
echo -e "${GREEN}===================${NC}"
echo -e "${GREEN}✅ Core infrastructure deployed and accessible${NC}"
echo -e "${GREEN}✅ BigQuery dataset and tables created${NC}"
echo -e "${GREEN}✅ Dashboard views available${NC}"
echo -e "${GREEN}✅ Security configuration in place${NC}"
echo -e "${GREEN}✅ Monitoring metrics configured${NC}"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Configure Google Drive sync for your NT8 logs"
echo "2. Schedule PowerShell script for daily log organization"
echo "3. Test with real log data using:"
echo "   curl -X POST \"$SERVICE_URL/process\" \\"
echo "     -H \"Content-Type: application/json\" \\"
echo "     -d '{\"folder_path\": \"gs://your-bucket/MES_Mancini/YYYYMMDD\"}'"
echo "4. Create Looker Studio dashboard using the template"
echo ""
echo -e "${GREEN}🚀 Pipeline is ready for production use!${NC}"

# Cleanup
rm -f /tmp/health_response.json /tmp/bq_test.txt /tmp/session_schema.json /tmp/trades_schema.json /tmp/process_response.json
