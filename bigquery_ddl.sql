-- ManciniMES Trading Analytics - BigQuery Schema
-- <PERSON>les "no trading days" with proper NULL handling and date continuity

-- Main fact table for all trades
CREATE OR REPLACE TABLE `mes_mancini.trades_fact` (
  -- Run identification
  run_id STRING NOT NULL,
  run_date DATE NOT NULL,
  strategy_version STRING,
  mode STRING, -- Live, Playback, Replay
  account STRING,
  instrument STRING,
  config_hash STRING,
  
  -- Trade identification (use full UUID as primary key)
  full_trade_id STRING NOT NULL,
  trade_id STRING, -- Short ID for convenience
  
  -- Entry details
  signal STRING,
  entry_time TIMESTAMP,
  entry_price NUMERIC(10,4),
  contracts INTEGER,
  stop_price NUMERIC(10,4),
  target_price NUMERIC(10,4),
  actual_risk NUMERIC(10,2),
  stop_distance NUMERIC(6,2),
  support_level NUMERIC(10,4),
  breakdown_low NUMERIC(10,4),
  
  -- Exit details
  exit_time TIMESTAMP,
  exit_price NUMERIC(10,4),
  exit_reason STRING,
  
  -- Performance metrics
  total_pnl NUMERIC(10,2),
  points_per_contract NUMERIC(6,2),
  r_multiple NUMERIC(6,3),
  mfe NUMERIC(10,2),
  mfe_capture_percent NUMERIC(5,2),
  duration_minutes INTEGER,
  
  -- MFE Trail details
  trail_armed BOOLEAN DEFAULT FALSE,
  trail_threshold NUMERIC(10,2),
  trail_peak_price NUMERIC(10,4),
  trail_exit_reason STRING,
  
  -- Risk management
  risk_tier STRING,
  calculated_size INTEGER,
  was_blocked BOOLEAN DEFAULT FALSE,
  block_reason STRING,
  
  -- Session metadata
  max_risk_setting NUMERIC(10,2),
  stop_atr_setting NUMERIC(3,1),
  max_trades_setting INTEGER,
  mfe_arm_setting NUMERIC(3,1),
  
  -- Audit trail
  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  ingest_hash STRING, -- Hash of source log content for idempotency
  
  -- Source references for click-through debugging
  src_trade_lines ARRAY<STRING>,
  src_mfe_lines ARRAY<STRING>,
  src_risk_lines ARRAY<STRING>
)
PARTITION BY run_date
CLUSTER BY signal, exit_reason, mode;

-- Daily session metadata (handles no-trading days)
CREATE OR REPLACE TABLE `mes_mancini.daily_sessions` (
  session_date DATE NOT NULL,
  run_id STRING,
  strategy_version STRING,
  mode STRING,
  account STRING,
  config_hash STRING,
  
  -- Session activity
  has_trades BOOLEAN DEFAULT FALSE,
  trade_count INTEGER DEFAULT 0,
  total_pnl NUMERIC(10,2) DEFAULT 0,
  
  -- Performance counters
  bars_processed INTEGER,
  ticks_processed INTEGER,
  session_hours NUMERIC(5,2),
  io_writes INTEGER,
  debounced_messages INTEGER,
  debounce_savings_percent NUMERIC(5,2),
  
  -- Session status
  session_complete BOOLEAN DEFAULT FALSE,
  missing_files ARRAY<STRING>, -- Track which log files were missing
  
  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY session_date;

-- Pattern events for signal analysis
CREATE OR REPLACE TABLE `mes_mancini.pattern_events` (
  run_id STRING NOT NULL,
  run_date DATE NOT NULL,
  
  pattern_type STRING,
  level_name STRING,
  support_price NUMERIC(10,4),
  breakdown_low NUMERIC(10,4),
  current_price NUMERIC(10,4),
  signal_strength STRING,
  timestamp TIMESTAMP,
  
  -- Link to resulting trade (if any)
  resulting_trade_id STRING,
  
  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY run_date
CLUSTER BY pattern_type, level_name;

-- Risk events for compliance monitoring
CREATE OR REPLACE TABLE `mes_mancini.risk_events` (
  run_id STRING NOT NULL,
  run_date DATE NOT NULL,
  
  trade_id STRING, -- NULL for session-level events
  event_type STRING, -- SIZING, GUARD, BLOCK, PROTECTIVE_STOP
  risk_tier STRING,
  calculated_size INTEGER,
  risk_amount NUMERIC(10,2),
  stop_distance NUMERIC(6,2),
  block_reason STRING,
  timestamp TIMESTAMP,
  
  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY run_date
CLUSTER BY event_type, risk_tier;

-- MFE trail events for detailed trail analysis
CREATE OR REPLACE TABLE `mes_mancini.mfe_trail_events` (
  run_id STRING NOT NULL,
  run_date DATE NOT NULL,

  trade_id STRING, -- Short trade ID for correlation
  event_type STRING, -- ARMED, UPDATE, EXIT_TRIGGER
  sequence INTEGER, -- MFE update sequence number
  mfe_amount NUMERIC(10,2), -- Current MFE in dollars
  threshold NUMERIC(10,2), -- Trail arm threshold
  is_armed BOOLEAN DEFAULT FALSE, -- Whether trail is armed
  peak_price NUMERIC(10,4), -- Peak favorable price
  current_price NUMERIC(10,4), -- Current market price
  price_drop NUMERIC(6,2), -- Price drop from peak in points
  trail_reason STRING, -- Trail trigger reason
  timestamp TIMESTAMP, -- Event timestamp

  ingest_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY run_date
CLUSTER BY event_type, trade_id;

-- Daily coaching dashboard view
CREATE OR REPLACE VIEW `mes_mancini.daily_coach` AS
SELECT 
  run_date,
  run_id,
  mode,
  
  -- Trading activity
  COUNT(*) as trades,
  SUM(total_pnl) as net_pnl,
  AVG(r_multiple) as avg_r_multiple,
  AVG(mfe_capture_percent) as avg_capture_pct,
  
  -- Exit breakdown
  COUNTIF(exit_reason LIKE '%Trail%') as trail_exits,
  COUNTIF(exit_reason LIKE '%Target%') as target_exits, 
  COUNTIF(exit_reason LIKE '%Stop%') as stop_exits,
  
  -- Performance metrics
  AVG(duration_minutes) as avg_duration_min,
  MAX(mfe) as max_mfe,
  MIN(total_pnl) as worst_trade,
  MAX(total_pnl) as best_trade,
  
  -- Risk compliance
  AVG(actual_risk) as avg_risk,
  MAX(actual_risk) as max_risk,
  COUNTIF(was_blocked) as blocked_entries
  
FROM `mes_mancini.trades_fact`
WHERE run_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
GROUP BY run_date, run_id, mode
ORDER BY run_date DESC;

-- Breakdown depth performance analysis
CREATE OR REPLACE VIEW `mes_mancini.depth_performance` AS
SELECT
  -- Breakdown depth buckets
  CASE 
    WHEN ABS(support_level - breakdown_low) <= 2 THEN '0-2pts'
    WHEN ABS(support_level - breakdown_low) <= 4 THEN '2-4pts'  
    WHEN ABS(support_level - breakdown_low) <= 6 THEN '4-6pts'
    WHEN ABS(support_level - breakdown_low) <= 8 THEN '6-8pts'
    WHEN ABS(support_level - breakdown_low) <= 10 THEN '8-10pts'
    ELSE '10+pts'
  END as breakdown_depth,
  
  COUNT(*) as trade_count,
  AVG(r_multiple) as avg_r_multiple,
  SUM(total_pnl) as net_pnl,
  AVG(mfe_capture_percent) as avg_capture_pct,
  
  -- Success metrics
  COUNTIF(total_pnl > 0) / COUNT(*) as win_rate,
  AVG(duration_minutes) as avg_duration_min
  
FROM `mes_mancini.trades_fact`
WHERE run_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
  AND support_level IS NOT NULL 
  AND breakdown_low IS NOT NULL
GROUP BY breakdown_depth
ORDER BY 
  CASE breakdown_depth
    WHEN '0-2pts' THEN 1
    WHEN '2-4pts' THEN 2  
    WHEN '4-6pts' THEN 3
    WHEN '6-8pts' THEN 4
    WHEN '8-10pts' THEN 5
    ELSE 6
  END;

-- Time-of-day performance heatmap
CREATE OR REPLACE VIEW `mes_mancini.time_performance` AS
SELECT
  EXTRACT(HOUR FROM entry_time) as entry_hour,
  EXTRACT(MINUTE FROM entry_time) as entry_minute,
  -- 15-minute buckets
  CONCAT(
    CAST(EXTRACT(HOUR FROM entry_time) AS STRING), 
    ':',
    CASE 
      WHEN EXTRACT(MINUTE FROM entry_time) < 15 THEN '00'
      WHEN EXTRACT(MINUTE FROM entry_time) < 30 THEN '15'  
      WHEN EXTRACT(MINUTE FROM entry_time) < 45 THEN '30'
      ELSE '45'
    END
  ) as time_bucket,
  
  COUNT(*) as trade_count,
  AVG(r_multiple) as avg_r_multiple,
  AVG(mfe_capture_percent) as avg_capture_pct,
  SUM(total_pnl) as net_pnl,
  COUNTIF(total_pnl > 0) / COUNT(*) as win_rate
  
FROM `mes_mancini.trades_fact`
WHERE run_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 60 DAY)
  AND entry_time IS NOT NULL
GROUP BY entry_hour, entry_minute, time_bucket
HAVING trade_count >= 2 -- Only show buckets with meaningful sample size
ORDER BY entry_hour, entry_minute;

-- Runner contribution analysis
CREATE OR REPLACE VIEW `mes_mancini.runner_analysis` AS
SELECT
  run_date,
  COUNT(*) as total_trades,

  -- Profit target vs runner breakdown
  SUM(CASE WHEN exit_reason LIKE '%Target%' THEN total_pnl ELSE 0 END) as target_pnl,
  SUM(CASE WHEN exit_reason LIKE '%Trail%' THEN total_pnl ELSE 0 END) as runner_pnl,

  -- Runner contribution percentage
  SAFE_DIVIDE(
    SUM(CASE WHEN exit_reason LIKE '%Trail%' THEN total_pnl ELSE 0 END),
    NULLIF(SUM(total_pnl), 0)
  ) * 100 as runner_contribution_pct,

  -- Trail effectiveness
  AVG(CASE WHEN trail_armed THEN mfe_capture_percent END) as avg_trail_capture,
  COUNTIF(trail_armed) as trades_with_trail,
  SAFE_DIVIDE(COUNTIF(trail_armed), COUNT(*)) * 100 as trail_arm_rate

FROM `mes_mancini.trades_fact`
WHERE run_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
GROUP BY run_date
ORDER BY run_date DESC;
