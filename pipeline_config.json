{"deployment": {"project_id": "mmancinimes-data", "region": "us-central1", "service_name": "mes-log-extract", "dataset_id": "mes_mancini"}, "langextract": {"endpoint": "https://api.langextract.com", "provider": "gemini", "model": {"gemini": "gemini-1.5-pro", "openai": "gpt-4-turbo"}}, "monitoring": {"alert_email": "<EMAIL>", "notification_channels": [], "enable_slack": false, "slack_webhook": ""}, "cloud_run": {"memory": "4Gi", "cpu": 2, "timeout": 900, "concurrency": 10, "max_instances": 20, "min_instances": 0}, "bigquery": {"location": "US", "partition_expiration_days": 365, "enable_row_level_security": false}, "drive_sync": {"source_path": "C:\\Users\\<USER>\\Documents\\NinjaTrader 8\\logs\\ManciniMES", "destination_path": "C:\\Users\\<USER>\\Google Drive\\MES_Mancini", "schedule_time": "15:55"}, "features": {"enable_mfe_events": true, "enable_risk_events": true, "enable_pattern_events": true, "enable_performance_counters": true, "enable_session_continuity": true}}