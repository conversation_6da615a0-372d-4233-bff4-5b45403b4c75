#!/usr/bin/env python3
"""
ManciniMES Log Processing Pipeline - Cloud Run Handler
Processes trading logs and extracts structured data using LangExtract
Handles "no trading days" gracefully with proper session tracking
"""

import os
import json
import hashlib
import logging
import tempfile
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import re

from flask import Flask, request, jsonify
from google.cloud import bigquery
from google.cloud import storage
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
PROJECT_ID = os.environ.get('GOOGLE_CLOUD_PROJECT')
DATASET_ID = 'mes_mancini'
LANGEXTRACT_ENDPOINT = os.environ.get('LANGEXTRACT_ENDPOINT')
LANGEXTRACT_API_KEY = os.environ.get('LANGEXTRACT_API_KEY')

# Initialize clients
bq_client = bigquery.Client()
storage_client = storage.Client()

class ManciniLogProcessor:
    """Processes ManciniMES trading logs with no-trading-day handling"""
    
    def __init__(self):
        self.expected_files = [
            'Trades_', 'Risk_', 'Debug_', 'MFETrail_', 'Patterns_'
        ]
    
    def derive_run_id_from_path(self, folder_path: str) -> Tuple[str, date]:
        """
        Derive run_id and date from folder path or filenames
        Handles: Drive:/MES_Mancini/20250831/ or individual files
        """
        try:
            # Extract date from folder path
            date_match = re.search(r'(\d{8})', folder_path)
            if date_match:
                date_str = date_match.group(1)
                run_date = datetime.strptime(date_str, '%Y%m%d').date()
                
                # For now, use date as base run_id (will be refined with actual files)
                return f"{date_str}_MES_UNKNOWN", run_date
            
            raise ValueError(f"Could not extract date from path: {folder_path}")
            
        except Exception as e:
            logger.error(f"Error deriving run_id from path {folder_path}: {e}")
            raise
    
    def discover_log_files(self, folder_path: str) -> Dict[str, str]:
        """
        Discover available log files in the folder
        Returns dict mapping file type to content
        """
        discovered_files = {}
        missing_files = []
        
        try:
            # Parse folder path to get bucket and prefix
            if folder_path.startswith('gs://'):
                # GCS path
                bucket_name = folder_path.split('/')[2]
                prefix = '/'.join(folder_path.split('/')[3:])
                bucket = storage_client.bucket(bucket_name)
                
                blobs = bucket.list_blobs(prefix=prefix)
                for blob in blobs:
                    filename = blob.name.split('/')[-1]
                    for file_type in self.expected_files:
                        if filename.startswith(file_type):
                            content = blob.download_as_text()
                            discovered_files[file_type.rstrip('_')] = content
                            break
            
            else:
                # Local path (for testing)
                folder = Path(folder_path)
                for file_path in folder.glob('*.txt'):
                    filename = file_path.name
                    for file_type in self.expected_files:
                        if filename.startswith(file_type):
                            with open(file_path, 'r', encoding='utf-8') as f:
                                discovered_files[file_type.rstrip('_')] = f.read()
                            break
            
            # Check for missing files
            for file_type in self.expected_files:
                key = file_type.rstrip('_')
                if key not in discovered_files:
                    missing_files.append(file_type + '*.txt')
            
            logger.info(f"Discovered {len(discovered_files)} files, missing {len(missing_files)}")
            return discovered_files, missing_files
            
        except Exception as e:
            logger.error(f"Error discovering files in {folder_path}: {e}")
            return {}, self.expected_files
    
    def extract_run_metadata(self, risk_content: str) -> Dict:
        """Extract run metadata from Risk log headers"""
        metadata = {}
        
        try:
            lines = risk_content.split('\n')[:10]  # Check first 10 lines
            
            for line in lines:
                if 'Strategy: Adam Mancini MES' in line:
                    # Parse: Strategy: Adam Mancini MES v2.3.0 | Mode: Live | Account: SIM1**** | Instrument: MES
                    parts = line.split('|')
                    for part in parts:
                        part = part.strip()
                        if 'Strategy:' in part:
                            version_match = re.search(r'v(\d+\.\d+\.\d+)', part)
                            if version_match:
                                metadata['strategy_version'] = version_match.group(1)
                        elif 'Mode:' in part:
                            metadata['mode'] = part.split(':')[1].strip()
                        elif 'Account:' in part:
                            metadata['account'] = part.split(':')[1].strip()
                        elif 'Instrument:' in part:
                            metadata['instrument'] = part.split(':')[1].strip()
                
                elif 'ConfigHash:' in line:
                    # Parse: Timezone: America/New_York | Start: 10:02:06.205 ET | ConfigHash: 5c24789b
                    config_match = re.search(r'ConfigHash:\s*([a-f0-9]+)', line)
                    if config_match:
                        metadata['config_hash'] = config_match.group(1)
                
                elif 'MaxRisk:' in line:
                    # Parse: MaxRisk: $155 | StopATR: 2.0x | MaxTrades: 3 | MFEArm: 1.2x
                    risk_match = re.search(r'MaxRisk:\s*\$(\d+)', line)
                    atr_match = re.search(r'StopATR:\s*([\d.]+)x', line)
                    trades_match = re.search(r'MaxTrades:\s*(\d+)', line)
                    mfe_match = re.search(r'MFEArm:\s*([\d.]+)x', line)
                    
                    if risk_match:
                        metadata['max_risk'] = float(risk_match.group(1))
                    if atr_match:
                        metadata['stop_atr'] = float(atr_match.group(1))
                    if trades_match:
                        metadata['max_trades'] = int(trades_match.group(1))
                    if mfe_match:
                        metadata['mfe_arm'] = float(mfe_match.group(1))
        
        except Exception as e:
            logger.warning(f"Error extracting metadata: {e}")
        
        return metadata
    
    def refine_run_id(self, base_run_id: str, files: Dict[str, str]) -> str:
        """Refine run_id using actual filenames from log content"""
        try:
            # Look for actual filenames in any log content
            for content in files.values():
                lines = content.split('\n')[:5]  # Check first few lines
                for line in lines:
                    # Look for patterns like: Trades_20250831_MES_SIM101.txt
                    filename_match = re.search(r'(\d{8})_([A-Z]+)_([A-Za-z0-9*]+)', line)
                    if filename_match:
                        date_str, symbol, account = filename_match.groups()
                        return f"{date_str}_{symbol}_{account}"
            
            # Fallback: try to extract from Risk log metadata
            if 'Risk' in files:
                metadata = self.extract_run_metadata(files['Risk'])
                if 'account' in metadata:
                    account = metadata['account'].replace('*', '')
                    date_str = base_run_id.split('_')[0]
                    return f"{date_str}_MES_{account}"
            
            return base_run_id
            
        except Exception as e:
            logger.warning(f"Error refining run_id: {e}")
            return base_run_id
    
    def call_langextract(self, files: Dict[str, str], run_id: str) -> Dict:
        """Call LangExtract API to process the log files"""
        try:
            # Import LangExtract client
            from langextract_config import LangExtractClient

            # Load extraction spec
            with open('langextract_spec.json', 'r') as f:
                spec = json.load(f)

            # Prepare multi-document input
            documents = []
            for file_type, content in files.items():
                documents.append({
                    'id': f"{run_id}_{file_type}",
                    'content': content,
                    'metadata': {'file_type': file_type, 'run_id': run_id}
                })

            # Get provider from environment (default to gemini)
            provider = os.environ.get('LANGEXTRACT_PROVIDER', 'gemini')

            # Create client and extract
            client = LangExtractClient(provider)
            result = client.extract_from_logs(documents, spec['extraction_spec'])

            logger.info(f"LangExtract completed with provider {provider}: {len(result.get('extractions', []))} extractions")
            return result

        except Exception as e:
            logger.error(f"LangExtract API call failed: {e}")
            raise
    
    def calculate_content_hash(self, files: Dict[str, str]) -> str:
        """Calculate hash of all file contents for idempotency"""
        import hashlib

        combined_content = ""
        for file_type in sorted(files.keys()):
            combined_content += files[file_type]

        hash_obj = hashlib.sha256(combined_content.encode('utf-8'))
        return hash_obj.hexdigest()[:16]  # First 16 chars

    def check_already_processed(self, run_id: str, run_date: date, content_hash: str) -> bool:
        """Check if this exact content has already been processed"""
        try:
            sql = f"""
            SELECT 1 FROM `{PROJECT_ID}.{DATASET_ID}.session_status`
            WHERE session_date = @d AND run_id = @r AND content_hash = @h AND processing_status = 'COMPLETE'
            LIMIT 1
            """
            params = [
                bigquery.ScalarQueryParameter("d", "DATE", run_date.isoformat()),
                bigquery.ScalarQueryParameter("r", "STRING", run_id),
                bigquery.ScalarQueryParameter("h", "STRING", content_hash),
            ]

            results = list(bq_client.query(sql, job_config=bigquery.QueryJobConfig(query_parameters=params)))
            return len(results) > 0

        except Exception as e:
            logger.warning(f"Error checking processing status: {e}")
            return False

    def process_folder(self, folder_path: str) -> Dict:
        """Main processing pipeline for a log folder with idempotency"""
        try:
            # Step 1: Derive basic run info
            base_run_id, run_date = self.derive_run_id_from_path(folder_path)
            logger.info(f"Processing folder: {folder_path} -> {base_run_id}, {run_date}")

            # Step 2: Discover available files
            files, missing_files = self.discover_log_files(folder_path)
            files_found = len(files)  # Track actual discovered file count

            # Step 3: Determine session status
            if not files:
                logger.warning(f"No log files found in {folder_path}")
                return self.record_session_status(base_run_id, run_date, 'NO_DATA', 0, missing_files)

            # Step 4: Calculate content hash for idempotency
            content_hash = self.calculate_content_hash(files)

            # Step 5: Refine run_id with actual data
            run_id = self.refine_run_id(base_run_id, files)
            logger.info(f"Refined run_id: {run_id}, content_hash: {content_hash}")

            # Step 6: Check if already processed
            if self.check_already_processed(run_id, run_date, content_hash):
                logger.info(f"Content already processed for {run_id}, skipping")
                return {'status': 'already_processed', 'run_id': run_id, 'content_hash': content_hash}

            # Step 7: Determine session status
            session_status = 'INCOMPLETE' if missing_files else 'ACTIVE'

            # Step 8: Extract metadata
            metadata = {}
            if 'Risk' in files:
                metadata = self.extract_run_metadata(files['Risk'])

            # Step 9: Call LangExtract
            extraction_result = self.call_langextract(files, run_id)

            # Step 10: Process and store results (pass actual files_found count)
            return self.store_results(run_id, run_date, extraction_result, metadata, missing_files, content_hash, session_status, files_found)

        except Exception as e:
            logger.error(f"Error processing folder {folder_path}: {e}")
            # Record error status
            try:
                files_found = len(files) if 'files' in locals() else 0
                self.record_session_status(base_run_id or 'unknown', run_date or date.today(), 'ERROR', files_found, missing_files or [], None)
            except:
                pass
            raise
    
    def record_session_status(self, run_id: str, run_date: date, session_status: str,
                             files_found: int, missing_files: List[str], content_hash: str = None) -> Dict:
        """Record session status with proper parameterization (idempotent)."""
        try:
            sql = f"""
            MERGE `{PROJECT_ID}.{DATASET_ID}.session_status` T
            USING (
              SELECT
                @session_date AS session_date,
                @run_id AS run_id,
                @session_status AS session_status,
                @files_expected AS files_expected,
                @files_found AS files_found,
                (SELECT ARRAY_AGG(x) FROM UNNEST(@files_missing) AS x) AS files_missing,
                @content_hash AS content_hash,
                @processing_status AS processing_status,
                CURRENT_TIMESTAMP() AS folder_created_at,
                CURRENT_TIMESTAMP() AS processed_at
            ) S
            ON T.session_date = S.session_date AND T.run_id = S.run_id
            WHEN MATCHED THEN UPDATE SET
              session_status = S.session_status,
              files_found = S.files_found,
              files_missing = S.files_missing,
              content_hash = S.content_hash,
              processing_status = S.processing_status,
              processed_at = S.processed_at
            WHEN NOT MATCHED THEN
            INSERT (
              session_date, run_id, session_status, files_expected, files_found,
              files_missing, content_hash, processing_status, folder_created_at, processed_at
            )
            VALUES (
              S.session_date, S.run_id, S.session_status, S.files_expected, S.files_found,
              S.files_missing, S.content_hash, S.processing_status, S.folder_created_at, S.processed_at
            )
            """
            params = [
                bigquery.ScalarQueryParameter("session_date", "DATE", run_date.isoformat()),
                bigquery.ScalarQueryParameter("run_id", "STRING", run_id),
                bigquery.ScalarQueryParameter("session_status", "STRING", session_status),
                bigquery.ScalarQueryParameter("files_expected", "INT64", len(self.expected_files)),
                bigquery.ScalarQueryParameter("files_found", "INT64", files_found),
                bigquery.ArrayQueryParameter("files_missing", "STRING", missing_files or []),
                bigquery.ScalarQueryParameter("content_hash", "STRING", content_hash or ""),
                bigquery.ScalarQueryParameter("processing_status", "STRING",
                                              "COMPLETE" if session_status != "ERROR" else "ERROR"),
            ]
            bq_client.query(sql, job_config=bigquery.QueryJobConfig(query_parameters=params)).result()
            logger.info("Recorded session_status=%s for %s", session_status, run_date)
            return {"status": session_status.lower(), "session_date": run_date.isoformat()}
        except Exception as e:
            logger.error("Error recording session status: %s", e)
            raise
    
    def _normalize_time_et(self, run_date: date, hms: str) -> str:
        """Return a time string for ET conversion, or None if missing."""
        if not hms:
            return None
        return hms  # 'HH:MM:SS.sss' - will be parsed in SQL

    def _build_trade_rows(self, run_id: str, run_date: date, extraction: Dict) -> List[Dict]:
        """Join class outputs into one dict per trade."""
        classes = extraction.get("extractions", extraction)

        # Normalize by class name → list of rows
        buckets = {}
        if isinstance(classes, dict):
            buckets = {k: v for k, v in classes.items() if isinstance(v, list)}
        else:
            # Flat list with {"class": "TradeSummary", "data": {...}}
            for item in classes:
                class_name = item.get("class") or item.get("name")
                data = item.get("data", item.get("fields", item))
                buckets.setdefault(class_name, []).append(data)

        def trade_key(row: Dict) -> str:
            return row.get("full_trade_id") or row.get("trade_id") or "unknown"

        facts = {}
        def ensure(key: str) -> Dict:
            if key not in facts:
                facts[key] = {
                    "run_id": run_id,
                    "run_date": run_date.isoformat(),
                    "full_trade_id": key,
                }
            return facts[key]

        # Start with summary lines (carry PnL, capture, r_multiple, reason)
        for row in buckets.get("TradeSummary", []):
            key = trade_key(row)
            f = ensure(key)
            f.update({
                "trade_id": row.get("trade_id"),
                "signal": row.get("signal"),
                "entry_time_hms": row.get("entry_time"),
                "exit_time_hms": row.get("exit_time"),
                "entry_price": row.get("entry_price"),
                "exit_price": row.get("exit_price"),
                "contracts": row.get("contracts"),
                "exit_reason": row.get("exit_reason"),
                "total_pnl": row.get("total_pnl"),
                "r_multiple": row.get("r_multiple"),
                "mfe": row.get("mfe"),
                "mfe_capture_percent": row.get("mfe_capture_percent"),
                "duration_minutes": row.get("duration_minutes"),
                "trail_armed": row.get("trail_armed", False),
            })

        # Fill from TradeEntry
        for row in buckets.get("TradeEntry", []):
            key = trade_key(row)
            f = ensure(key)
            f.update({
                "trade_id": row.get("trade_id"),
                "signal": row.get("signal"),
                "entry_time_hms": row.get("entry_time"),
                "entry_price": row.get("entry_price"),
                "contracts": row.get("contracts"),
                "stop_price": row.get("stop_price"),
                "target_price": row.get("target_price"),
                "actual_risk": row.get("actual_risk"),
                "stop_distance": row.get("stop_distance"),
                "support_level": row.get("support_level"),
                "breakdown_low": row.get("breakdown_low"),
            })

        # Fill from TradeExit
        for row in buckets.get("TradeExit", []):
            key = trade_key(row)
            f = ensure(key)
            f.update({
                "exit_time_hms": row.get("exit_time"),
                "exit_price": row.get("exit_price"),
                "exit_reason": row.get("exit_reason"),
                "total_pnl": row.get("pnl"),
                "points_per_contract": row.get("points_per_contract"),
            })

        # Convert times to strings for SQL parsing
        for f in facts.values():
            f["entry_time_hms"] = self._normalize_time_et(run_date, f.get("entry_time_hms"))
            f["exit_time_hms"] = self._normalize_time_et(run_date, f.get("exit_time_hms"))

        return list(facts.values())

    def store_results(self, run_id: str, run_date: date, extraction_result: Dict,
                     metadata: Dict, missing_files: List[str], content_hash: str, session_status: str, files_found: int) -> Dict:
        """Load to staging then MERGE into trades_fact (idempotent)."""
        try:
            rows = self._build_trade_rows(run_id, run_date, extraction_result)
            logger.info(f"Built {len(rows)} trade rows for {run_id}")

            if rows:
                # Create staging table and load
                stg_table = f"{PROJECT_ID}.{DATASET_ID}._stg_trades_fact"
                self._load_to_staging(rows, stg_table)

                # MERGE into trades_fact with timezone conversion
                self._merge_from_staging(stg_table)

                # Clean up staging table
                try:
                    bq_client.delete_table(stg_table)
                except:
                    pass  # Ignore cleanup errors

            # Update session status
            final_status = 'ACTIVE' if rows else 'NO_TRADES'
            if session_status == 'INCOMPLETE':
                final_status = 'INCOMPLETE'

            # Use actual discovered file count passed from process_folder
            self.record_session_status(run_id, run_date, final_status, files_found, missing_files, content_hash)

            # Update daily session summary
            self._update_daily_summary(run_id, run_date, rows, final_status)

            return {
                'status': 'success',
                'run_id': run_id,
                'run_date': run_date.isoformat(),
                'trades_processed': len(rows),
                'session_status': final_status,
                'missing_files': missing_files
            }

        except Exception as e:
            logger.error(f"Error storing results: {e}")
            # Record error status
            self.record_session_status(run_id, run_date, 'ERROR', 0, missing_files, content_hash)
            raise

    def _load_to_staging(self, rows: List[Dict], stg_table: str) -> None:
        """Load rows to staging table with explicit schema."""
        schema = [
            bigquery.SchemaField("run_id", "STRING"),
            bigquery.SchemaField("run_date", "DATE"),
            bigquery.SchemaField("full_trade_id", "STRING"),
            bigquery.SchemaField("trade_id", "STRING"),
            bigquery.SchemaField("signal", "STRING"),
            bigquery.SchemaField("entry_time_hms", "STRING"),
            bigquery.SchemaField("exit_time_hms", "STRING"),
            bigquery.SchemaField("entry_price", "NUMERIC"),
            bigquery.SchemaField("exit_price", "NUMERIC"),
            bigquery.SchemaField("contracts", "INTEGER"),
            bigquery.SchemaField("stop_price", "NUMERIC"),
            bigquery.SchemaField("target_price", "NUMERIC"),
            bigquery.SchemaField("actual_risk", "NUMERIC"),
            bigquery.SchemaField("stop_distance", "NUMERIC"),
            bigquery.SchemaField("support_level", "NUMERIC"),
            bigquery.SchemaField("breakdown_low", "NUMERIC"),
            bigquery.SchemaField("exit_reason", "STRING"),
            bigquery.SchemaField("total_pnl", "NUMERIC"),
            bigquery.SchemaField("points_per_contract", "NUMERIC"),
            bigquery.SchemaField("r_multiple", "NUMERIC"),
            bigquery.SchemaField("mfe", "NUMERIC"),
            bigquery.SchemaField("mfe_capture_percent", "NUMERIC"),
            bigquery.SchemaField("duration_minutes", "INTEGER"),
            bigquery.SchemaField("trail_armed", "BOOLEAN"),
        ]

        # Create or replace staging table
        table = bigquery.Table(stg_table, schema=schema)
        table = bq_client.create_table(table, exists_ok=True)

        # Load data
        job_config = bigquery.LoadJobConfig(
            schema=schema,
            write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
        )

        job = bq_client.load_table_from_json(rows, table, job_config=job_config)
        job.result()  # Wait for completion

        logger.info(f"Loaded {len(rows)} rows to staging table")

    def _merge_from_staging(self, stg_table: str) -> None:
        """MERGE from staging table with timezone-aware timestamps."""
        merge_sql = f"""
        MERGE `{PROJECT_ID}.{DATASET_ID}.trades_fact` T
        USING (
          SELECT
            run_id,
            DATE(run_date) AS run_date,
            full_trade_id,
            trade_id,
            signal,
            -- Convert HH:MM:SS.sss + run_date into ET timestamps
            CASE WHEN entry_time_hms IS NOT NULL THEN
              TIMESTAMP(DATETIME(run_date, PARSE_TIME('%H:%M:%E*S', entry_time_hms)), 'America/New_York')
            ELSE NULL END AS entry_time,
            CASE WHEN exit_time_hms IS NOT NULL THEN
              TIMESTAMP(DATETIME(run_date, PARSE_TIME('%H:%M:%E*S', exit_time_hms)), 'America/New_York')
            ELSE NULL END AS exit_time,
            CAST(entry_price AS NUMERIC) AS entry_price,
            CAST(exit_price AS NUMERIC) AS exit_price,
            contracts,
            CAST(stop_price AS NUMERIC) AS stop_price,
            CAST(target_price AS NUMERIC) AS target_price,
            CAST(actual_risk AS NUMERIC) AS actual_risk,
            CAST(stop_distance AS NUMERIC) AS stop_distance,
            CAST(support_level AS NUMERIC) AS support_level,
            CAST(breakdown_low AS NUMERIC) AS breakdown_low,
            exit_reason,
            CAST(total_pnl AS NUMERIC) AS total_pnl,
            CAST(points_per_contract AS NUMERIC) AS points_per_contract,
            CAST(r_multiple AS NUMERIC) AS r_multiple,
            CAST(mfe AS NUMERIC) AS mfe,
            CAST(mfe_capture_percent AS NUMERIC) AS mfe_capture_percent,
            duration_minutes,
            trail_armed
          FROM `{stg_table}`
        ) S
        ON T.run_id = S.run_id AND T.full_trade_id = S.full_trade_id
        WHEN MATCHED THEN UPDATE SET
          trade_id = S.trade_id,
          signal = S.signal,
          entry_time = S.entry_time,
          exit_time = S.exit_time,
          entry_price = S.entry_price,
          exit_price = S.exit_price,
          contracts = S.contracts,
          stop_price = S.stop_price,
          target_price = S.target_price,
          actual_risk = S.actual_risk,
          stop_distance = S.stop_distance,
          support_level = S.support_level,
          breakdown_low = S.breakdown_low,
          exit_reason = S.exit_reason,
          total_pnl = S.total_pnl,
          points_per_contract = S.points_per_contract,
          r_multiple = S.r_multiple,
          mfe = S.mfe,
          mfe_capture_percent = S.mfe_capture_percent,
          duration_minutes = S.duration_minutes,
          trail_armed = S.trail_armed,
          ingest_timestamp = CURRENT_TIMESTAMP()
        WHEN NOT MATCHED THEN INSERT (
          run_id, run_date, full_trade_id, trade_id, signal, entry_time, exit_time,
          entry_price, exit_price, contracts, stop_price, target_price, actual_risk,
          stop_distance, support_level, breakdown_low, exit_reason, total_pnl,
          points_per_contract, r_multiple, mfe, mfe_capture_percent, duration_minutes,
          trail_armed, ingest_timestamp
        ) VALUES (
          S.run_id, S.run_date, S.full_trade_id, S.trade_id, S.signal, S.entry_time, S.exit_time,
          S.entry_price, S.exit_price, S.contracts, S.stop_price, S.target_price, S.actual_risk,
          S.stop_distance, S.support_level, S.breakdown_low, S.exit_reason, S.total_pnl,
          S.points_per_contract, S.r_multiple, S.mfe, S.mfe_capture_percent, S.duration_minutes,
          S.trail_armed, CURRENT_TIMESTAMP()
        )
        """

        job = bq_client.query(merge_sql)
        job.result()  # Wait for completion

        logger.info("MERGE completed successfully")



    def _update_daily_summary(self, run_id: str, run_date: date, trades: List[Dict], session_status: str) -> None:
        """Update daily session summary with aggregated metrics"""
        if not trades:
            # No trades - just record session
            summary_data = {
                'session_date': run_date.isoformat(),
                'has_strategy_run': True,
                'has_trades': False,
                'trade_count': 0,
                'total_pnl': 0
            }
        else:
            # Calculate aggregated metrics
            total_pnl = sum(float(t.get('total_pnl', 0)) for t in trades)
            r_multiples = [float(t.get('r_multiple', 0)) for t in trades if t.get('r_multiple')]
            avg_r = sum(r_multiples) / len(r_multiples) if r_multiples else 0

            summary_data = {
                'session_date': run_date.isoformat(),
                'has_strategy_run': True,
                'has_trades': True,
                'trade_count': len(trades),
                'total_pnl': total_pnl,
                'avg_r_multiple': avg_r,
                'best_trade': max((float(t.get('total_pnl', 0)) for t in trades), default=0),
                'worst_trade': min((float(t.get('total_pnl', 0)) for t in trades), default=0),
                'target_exits': sum(1 for t in trades if 'Target' in t.get('exit_reason', '')),
                'trail_exits': sum(1 for t in trades if 'Trail' in t.get('exit_reason', '')),
                'stop_exits': sum(1 for t in trades if 'Stop' in t.get('exit_reason', ''))
            }

        # MERGE into daily_session_summary
        merge_query = f"""
        MERGE `{PROJECT_ID}.{DATASET_ID}.daily_session_summary` T
        USING (SELECT
            DATE('{run_date.isoformat()}') as session_date,
            {summary_data['has_strategy_run']} as has_strategy_run,
            {summary_data['has_trades']} as has_trades,
            {summary_data['trade_count']} as trade_count,
            {summary_data['total_pnl']} as total_pnl,
            {summary_data.get('avg_r_multiple', 0)} as avg_r_multiple,
            {summary_data.get('best_trade', 0)} as best_trade,
            {summary_data.get('worst_trade', 0)} as worst_trade,
            {summary_data.get('target_exits', 0)} as target_exits,
            {summary_data.get('trail_exits', 0)} as trail_exits,
            {summary_data.get('stop_exits', 0)} as stop_exits
        ) S
        ON T.session_date = S.session_date
        WHEN MATCHED THEN UPDATE SET
            has_strategy_run = S.has_strategy_run,
            has_trades = S.has_trades,
            trade_count = S.trade_count,
            total_pnl = S.total_pnl,
            avg_r_multiple = S.avg_r_multiple,
            best_trade = S.best_trade,
            worst_trade = S.worst_trade,
            target_exits = S.target_exits,
            trail_exits = S.trail_exits,
            stop_exits = S.stop_exits,
            ingest_timestamp = CURRENT_TIMESTAMP()
        WHEN NOT MATCHED THEN INSERT (
            session_date, has_strategy_run, has_trades, trade_count, total_pnl,
            avg_r_multiple, best_trade, worst_trade, target_exits, trail_exits,
            stop_exits, ingest_timestamp
        ) VALUES (
            S.session_date, S.has_strategy_run, S.has_trades, S.trade_count, S.total_pnl,
            S.avg_r_multiple, S.best_trade, S.worst_trade, S.target_exits, S.trail_exits,
            S.stop_exits, CURRENT_TIMESTAMP()
        )
        """

        merge_job = bq_client.query(merge_query)
        merge_job.result()

        logger.info(f"Updated daily summary for {run_date}")

    def _upsert_mfe_events(self, events: List[Dict], run_id: str, run_date: date) -> None:
        """Upsert MFE trail events with complete MERGE"""
        if not events:
            return

        # Prepare events for BigQuery
        bq_events = []
        for event in events:
            bq_event = {
                'run_id': run_id,
                'run_date': run_date.isoformat(),
                'trade_id': event.get('trade_id'),
                'event_type': event.get('event_type'),
                'sequence': event.get('sequence'),
                'mfe_amount': event.get('mfe_amount'),
                'threshold': event.get('threshold'),
                'is_armed': event.get('is_armed', False),
                'peak_price': event.get('peak_price'),
                'current_price': event.get('current_price'),
                'price_drop': event.get('price_drop'),
                'trail_reason': event.get('trail_reason'),
                'timestamp': event.get('timestamp')
            }
            bq_events.append(bq_event)

        # Insert using streaming (simpler for event data)
        table_ref = bq_client.dataset(DATASET_ID).table('mfe_trail_events')
        errors = bq_client.insert_rows_json(table_ref, bq_events)

        if errors:
            logger.error(f"MFE events insert errors: {errors}")
        else:
            logger.info(f"Inserted {len(bq_events)} MFE events")

    def _upsert_risk_events(self, events: List[Dict], run_id: str, run_date: date) -> None:
        """Upsert risk events with complete MERGE"""
        if not events:
            return

        # Prepare events for BigQuery
        bq_events = []
        for event in events:
            bq_event = {
                'run_id': run_id,
                'run_date': run_date.isoformat(),
                'trade_id': event.get('trade_id'),  # Can be NULL for session-level events
                'event_type': event.get('event_type'),
                'risk_tier': event.get('risk_tier'),
                'calculated_size': event.get('calculated_size'),
                'risk_amount': event.get('risk_amount'),
                'stop_distance': event.get('stop_distance'),
                'block_reason': event.get('block_reason'),
                'timestamp': event.get('timestamp')
            }
            bq_events.append(bq_event)

        # Insert using streaming
        table_ref = bq_client.dataset(DATASET_ID).table('risk_events')
        errors = bq_client.insert_rows_json(table_ref, bq_events)

        if errors:
            logger.error(f"Risk events insert errors: {errors}")
        else:
            logger.info(f"Inserted {len(bq_events)} risk events")

    def _upsert_pattern_events(self, events: List[Dict], run_id: str, run_date: date) -> None:
        """Upsert pattern events with complete MERGE"""
        if not events:
            return

        # Prepare events for BigQuery
        bq_events = []
        for event in events:
            bq_event = {
                'run_id': run_id,
                'run_date': run_date.isoformat(),
                'pattern_type': event.get('pattern_type'),
                'level_name': event.get('level_name'),
                'support_price': event.get('support_price'),
                'breakdown_low': event.get('breakdown_low'),
                'current_price': event.get('current_price'),
                'signal_strength': event.get('signal_strength'),
                'timestamp': event.get('timestamp'),
                'resulting_trade_id': None  # Will be linked later if needed
            }
            bq_events.append(bq_event)

        # Insert using streaming
        table_ref = bq_client.dataset(DATASET_ID).table('pattern_events')
        errors = bq_client.insert_rows_json(table_ref, bq_events)

        if errors:
            logger.error(f"Pattern events insert errors: {errors}")
        else:
            logger.info(f"Inserted {len(bq_events)} pattern events")

# Flask endpoints
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '2.0',
        'provider': os.environ.get('LANGEXTRACT_PROVIDER', 'gemini')
    })

@app.route('/process', methods=['POST'])
def process_logs():
    """Main processing endpoint triggered by Eventarc or manual backfill"""
    try:
        # Parse request
        data = request.get_json()
        folder_path = data.get('folder_path')

        if not folder_path:
            return jsonify({'error': 'folder_path required'}), 400

        # Process the folder
        processor = ManciniLogProcessor()
        result = processor.process_folder(folder_path)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Processing error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/finalize-day', methods=['POST'])
def finalize_day():
    """EOD finalization endpoint called by Cloud Scheduler"""
    try:
        data = request.get_json() or {}
        target_date = data.get('date', date.today().isoformat())

        # Check for incomplete sessions
        query = f"""
        SELECT session_date, run_id, session_status, files_missing
        FROM `{PROJECT_ID}.{DATASET_ID}.session_status`
        WHERE session_date = DATE('{target_date}')
          AND processing_status != 'COMPLETE'
        """

        incomplete_sessions = list(bq_client.query(query))

        if incomplete_sessions:
            logger.warning(f"Found {len(incomplete_sessions)} incomplete sessions for {target_date}")
            # Could trigger alerts here

        return jsonify({
            'status': 'finalized',
            'date': target_date,
            'incomplete_sessions': len(incomplete_sessions)
        })

    except Exception as e:
        logger.error(f"Finalization error: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)
