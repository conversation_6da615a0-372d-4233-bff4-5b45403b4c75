# ManciniMES Automated Analytics Pipeline v2.0 - Production Setup Guide

## 🎯 Overview

This production-ready pipeline automatically processes your ManciniMES trading logs and creates institutional-grade coaching dashboards. It handles all scenarios gracefully:

- ✅ **Normal Trading Days**: Complete log processing and analytics
- ✅ **No Trading Days**: Proper session tracking with status markers
- ✅ **Partial Data**: Handles missing log files gracefully
- ✅ **Idempotent Processing**: Safe to re-run without duplicates
- ✅ **Complete Audit Trails**: Full traceability and debugging

## 📋 Prerequisites

1. **Google Cloud Project** with billing enabled
2. **Google Drive for Desktop** installed
3. **NinjaTrader 8** with ManciniMES strategy running
4. **PowerShell** (Windows) for daily automation
5. **LangExtract API Key** (Gemini or OpenAI preferred)

## 🚀 Production Setup (20 minutes)

### Step 1: Configuration (Optional)

**For automated deployments, create `pipeline_config.json`:**
```json
{
  "deployment": {
    "project_id": "your-project-id",
    "region": "us-central1"
  },
  "monitoring": {
    "alert_email": "<EMAIL>"
  },
  "langextract": {
    "provider": "gemini"
  }
}
```

### Step 2: One-Click Google Cloud Deployment

```bash
# Set your project ID (or use config file)
export GOOGLE_CLOUD_PROJECT="your-project-id"

# Authenticate with Google Cloud
gcloud auth login
gcloud config set project $GOOGLE_CLOUD_PROJECT

# Deploy the complete pipeline (one command!)
chmod +x deploy_production.sh
./deploy_production.sh
```

**What this does automatically:**
- ✅ Enables all required APIs
- ✅ Creates BigQuery dataset with all tables and views
- ✅ Builds and deploys Cloud Run service with production settings
- ✅ Sets up IAM roles and service accounts
- ✅ Configures monitoring and alerting
- ✅ Creates Cloud Scheduler for EOD processing
- ✅ Tests the complete deployment

### Step 2: Google Drive Sync Setup

1. **Install Google Drive for Desktop**
2. **Create folder structure:**
   ```
   Google Drive/
   └── MES_Mancini/
       ├── ********/
       ├── ********/
       └── ...
   ```
3. **Configure sync** to include your NT8 logs folder

### Step 3: Daily Automation

1. **Configure PowerShell script:**
   ```powershell
   # Edit paths in daily_log_organizer.ps1
   $SourcePath = "C:\Users\<USER>\Documents\NinjaTrader 8\logs\ManciniMES"
   $DestinationPath = "C:\Users\<USER>\Google Drive\MES_Mancini"
   ```

2. **Schedule daily execution:**
   ```powershell
   # Run as Administrator
   schtasks /create /tn "ManciniMES Log Organizer" /tr "powershell.exe -File 'C:\path\to\daily_log_organizer.ps1'" /sc daily /st 16:00
   ```

### Step 4: LangExtract Configuration

**API keys are automatically configured during deployment via Secret Manager for production security.**

1. **Get API Key** from LangExtract (Gemini or OpenAI)
2. **Key is stored securely** in Google Secret Manager during deployment
3. **No manual configuration needed** - the deploy script handles everything

**To change provider after deployment:**
```bash
# Update provider (stored as environment variable)
gcloud run services update mes-log-extract \
  --region=us-central1 \
  --set-env-vars="LANGEXTRACT_PROVIDER=openai"  # or "gemini"

# Update API key (stored in Secret Manager)
echo "new-api-key" | gcloud secrets versions add langextract-api-key --data-file=-
```

## 📊 What You Get

### Automated Data Flow
```
NT8 Logs → Google Drive → Cloud Run → LangExtract → BigQuery → Looker Studio
```

### Professional Dashboards
- **Daily Coach**: Trades, PnL, R-multiples, capture rates
- **Breakdown Depth Analysis**: Performance by setup depth
- **Time-of-Day Heatmap**: Optimal trading windows
- **Runner Contribution**: Trail vs target performance
- **Risk Compliance**: Safety monitoring

### Enhanced Session Status Tracking
- **NO_DATA**: No strategy run detected (weekend, holiday, system down)
- **NO_TRADES**: Strategy ran but no trades executed
- **INCOMPLETE**: Some log files missing (partial data available)
- **ACTIVE**: Complete trading session with full data
- **ERROR**: Processing failed (with retry capability)

### Advanced Features
- **Idempotent Processing**: Content hashing prevents duplicate processing
- **Session Continuity**: Complete trading day timeline with gap analysis
- **Data Quality Monitoring**: Completeness tracking and alerts
- **Audit Trails**: Full source line references for debugging
- **Performance Metrics**: Processing efficiency and system health

## 🔧 Pipeline Components

### 1. Daily Log Organizer (PowerShell)
- **Runs at 4:00 PM ET** daily
- **Organizes logs** into dated folders
- **Creates no-data markers** for non-trading days
- **Syncs to Google Drive** automatically

### 2. Cloud Run Processor
- **Triggered by new folders** in Drive
- **Calls LangExtract** to parse logs
- **Extracts structured data** from all log types
- **Stores in BigQuery** with idempotency

### 3. BigQuery Analytics
- **Fact tables** for trades, patterns, risk events
- **Session tracking** for continuity monitoring
- **Professional views** for dashboard consumption
- **Audit trails** with source line references

### 4. Monitoring & Alerting
- **Processing error alerts** via email/Slack
- **EOD finalization** checks for completeness
- **Performance metrics** tracking
- **Data quality** monitoring

## 📈 Dashboard Metrics

### Daily Coach View
```sql
SELECT 
  run_date,
  COUNT(*) as trades,
  SUM(total_pnl) as net_pnl,
  AVG(r_multiple) as avg_r_multiple,
  AVG(mfe_capture_percent) as avg_capture_pct
FROM `mes_mancini.daily_coach`
WHERE run_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
ORDER BY run_date DESC
```

### Breakdown Depth Analysis
```sql
SELECT 
  breakdown_depth,
  trade_count,
  avg_r_multiple,
  net_pnl,
  win_rate
FROM `mes_mancini.depth_performance`
ORDER BY avg_r_multiple DESC
```

## 🛠️ Troubleshooting

### Common Issues

**1. No data appearing in BigQuery**
- Check Cloud Run logs for processing errors
- Verify Google Drive sync is working
- Confirm LangExtract API key is set

**2. PowerShell script not running**
- Check Task Scheduler configuration
- Verify file paths are correct
- Run manually first to test

**3. Missing log files**
- Pipeline handles this gracefully
- Check `daily_sessions` table for status
- Review `missing_files` array

### Debug Commands

```bash
# Check Cloud Run logs
gcloud logs read --service=mes-log-extract --limit=50

# Test processing manually
curl -X POST "https://your-service-url/process" \
  -H "Content-Type: application/json" \
  -d '{"folder_path": "gs://your-bucket/MES_Mancini/********"}'

# Query session status
bq query "SELECT * FROM mes_mancini.session_continuity ORDER BY session_date DESC LIMIT 10"
```

## 🎯 Success Metrics

After setup, you should see:
- ✅ **Daily log organization** happening automatically
- ✅ **BigQuery tables** populating with trade data
- ✅ **Looker dashboards** showing coaching metrics
- ✅ **No-data days** properly tracked
- ✅ **Email alerts** for any processing issues

## 🚀 Advanced Features

### Custom Analytics
- Add your own SQL views to BigQuery
- Create custom Looker Studio visualizations
- Set up additional alerting rules

### Multi-Account Support
- Pipeline supports multiple trading accounts
- Data is automatically segmented by account
- Row-level security available

### Historical Backfill
- Drop historical log folders into Drive
- Pipeline processes them automatically
- Idempotent MERGE prevents duplicates

## 📞 Support

**Pipeline Issues:**
- Check Cloud Run logs first
- Review BigQuery job history
- Test individual components

**Data Questions:**
- Query BigQuery directly for debugging
- Use source line references for audit trails
- Check extraction reports in Drive folders

**Dashboard Problems:**
- Verify BigQuery permissions
- Refresh Looker Studio data sources
- Check view definitions

---

**🏆 Result: Professional trading analytics with zero manual work!**
