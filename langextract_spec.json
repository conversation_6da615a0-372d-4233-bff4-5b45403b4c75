{"extraction_spec": {"name": "ManciniMES_Trading_Logs", "description": "Extract structured trading data from ManciniMES strategy logs with institutional-grade correlation", "version": "1.0", "classes": [{"name": "RunMetadata", "description": "Session metadata from run headers", "fields": [{"name": "run_id", "type": "string", "description": "Derived from filename: YYYYMMDD_SYMBOL_ACCOUNT"}, {"name": "strategy_version", "type": "string", "description": "Strategy version from header"}, {"name": "mode", "type": "string", "description": "Live, Playback, or Replay"}, {"name": "account", "type": "string", "description": "Masked account identifier"}, {"name": "instrument", "type": "string", "description": "Trading instrument (MES)"}, {"name": "config_hash", "type": "string", "description": "8-char configuration hash"}, {"name": "max_risk", "type": "number", "description": "Maximum risk per trade in dollars"}, {"name": "stop_atr", "type": "number", "description": "ATR multiplier for stops"}, {"name": "max_trades", "type": "integer", "description": "Maximum daily trades"}, {"name": "mfe_arm", "type": "number", "description": "MFE trail arm multiplier"}, {"name": "session_start", "type": "string", "description": "Session start timestamp"}, {"name": "timezone", "type": "string", "description": "Timezone (America/New_York)"}]}, {"name": "TradeEntry", "description": "Trade entry events with risk parameters", "fields": [{"name": "trade_id", "type": "string", "description": "Short trade ID (first 8 chars)"}, {"name": "full_trade_id", "type": "string", "description": "Full UUID trade identifier"}, {"name": "signal", "type": "string", "description": "Entry signal name (Support1Entry)"}, {"name": "entry_time", "type": "string", "description": "Entry execution timestamp"}, {"name": "entry_price", "type": "number", "description": "Entry fill price"}, {"name": "contracts", "type": "integer", "description": "Number of contracts"}, {"name": "stop_price", "type": "number", "description": "Initial stop loss price"}, {"name": "target_price", "type": "number", "description": "Profit target price"}, {"name": "actual_risk", "type": "number", "description": "Actual risk in dollars"}, {"name": "stop_distance", "type": "number", "description": "Stop distance in points"}, {"name": "support_level", "type": "number", "description": "Support level used"}, {"name": "breakdown_low", "type": "number", "description": "Breakdown low price"}]}, {"name": "TradeExit", "description": "Trade exit events and performance", "fields": [{"name": "trade_id", "type": "string", "description": "Short trade ID"}, {"name": "exit_time", "type": "string", "description": "Exit execution timestamp"}, {"name": "exit_price", "type": "number", "description": "Exit fill price"}, {"name": "exit_reason", "type": "string", "description": "Exit reason (Target, Trail, Stop)"}, {"name": "contracts_exited", "type": "integer", "description": "Contracts in this exit"}, {"name": "pnl", "type": "number", "description": "PnL for this exit in dollars"}, {"name": "points_per_contract", "type": "number", "description": "Points per contract"}, {"name": "is_partial", "type": "boolean", "description": "Whether this is a partial exit"}]}, {"name": "TradeSummary", "description": "Complete trade performance summary", "fields": [{"name": "trade_id", "type": "string", "description": "Short trade ID"}, {"name": "entry_price", "type": "number", "description": "Entry price"}, {"name": "exit_price", "type": "number", "description": "Final exit price"}, {"name": "total_pnl", "type": "number", "description": "Total trade PnL in dollars"}, {"name": "points_per_contract", "type": "number", "description": "Points per contract"}, {"name": "r_multiple", "type": "number", "description": "R-multiple (risk-adjusted return)"}, {"name": "mfe", "type": "number", "description": "Maximum Favorable Excursion in dollars"}, {"name": "mfe_capture_percent", "type": "number", "description": "MFE capture percentage"}, {"name": "duration_minutes", "type": "integer", "description": "Trade duration in minutes"}, {"name": "exit_reason", "type": "string", "description": "Final exit reason"}]}, {"name": "MFETrailEvent", "description": "MFE trail system events", "fields": [{"name": "trade_id", "type": "string", "description": "Short trade ID"}, {"name": "event_type", "type": "string", "description": "ARMED, UPDATE, or EXIT_TRIGGER"}, {"name": "sequence", "type": "integer", "description": "MFE update sequence number"}, {"name": "mfe_amount", "type": "number", "description": "Current MFE in dollars"}, {"name": "threshold", "type": "number", "description": "Trail arm threshold"}, {"name": "is_armed", "type": "boolean", "description": "Whether trail is armed"}, {"name": "peak_price", "type": "number", "description": "Peak favorable price"}, {"name": "current_price", "type": "number", "description": "Current market price"}, {"name": "price_drop", "type": "number", "description": "Price drop from peak in points"}, {"name": "trail_reason", "type": "string", "description": "Trail trigger reason"}, {"name": "timestamp", "type": "string", "description": "Event timestamp"}]}, {"name": "RiskEvent", "description": "Risk management events and blocks", "fields": [{"name": "trade_id", "type": "string", "description": "Trade ID if applicable"}, {"name": "event_type", "type": "string", "description": "SIZING, G<PERSON>AR<PERSON>, BLOCK, PROTECTIVE_STOP"}, {"name": "risk_tier", "type": "string", "description": "Risk tier (Conservative, Normal, Aggressive)"}, {"name": "calculated_size", "type": "integer", "description": "Calculated position size"}, {"name": "risk_amount", "type": "number", "description": "Risk amount in dollars"}, {"name": "stop_distance", "type": "number", "description": "Stop distance in points"}, {"name": "block_reason", "type": "string", "description": "Reason for entry block if applicable"}, {"name": "timestamp", "type": "string", "description": "Event timestamp"}]}, {"name": "PatternEvent", "description": "Pattern detection and entry signals", "fields": [{"name": "pattern_type", "type": "string", "description": "Pattern type detected"}, {"name": "level_name", "type": "string", "description": "Support level name"}, {"name": "support_price", "type": "number", "description": "Support level price"}, {"name": "breakdown_low", "type": "number", "description": "Breakdown low price"}, {"name": "current_price", "type": "number", "description": "Current market price"}, {"name": "signal_strength", "type": "string", "description": "Signal strength assessment"}, {"name": "timestamp", "type": "string", "description": "Pattern detection timestamp"}]}, {"name": "PerformanceMetrics", "description": "EOD performance counters", "fields": [{"name": "bars_processed", "type": "integer", "description": "Number of bars processed"}, {"name": "ticks_processed", "type": "integer", "description": "Number of ticks processed"}, {"name": "session_hours", "type": "number", "description": "Session duration in hours"}, {"name": "io_writes", "type": "integer", "description": "Total I/O write operations"}, {"name": "debounced_messages", "type": "integer", "description": "Messages debounced"}, {"name": "estimated_log_mb", "type": "number", "description": "Estimated log size in MB"}, {"name": "debounce_savings_percent", "type": "number", "description": "Debounce savings percentage"}]}], "examples": [{"input": "09:50:00.000 [TRADE] [trade=d297e798] ENTRY SUBMITTED | Signal: Support1Entry | EntryMode: STP-MKT | ExpectedFill: 6380.25 | OrderPrice: 6380.75 | CurrentPrice: 6380.00 | PriceDiff: 0.25pts | Size: 5 | ActualRisk: $143.75 | StopDistance: 5.75pts | Support: 6378.00 | BreakdownLow: 6375.50 | StopPrice: 6374.50 | TradeID: d297e798-5468-4695-8b3e-b0c8f417c067 | Time: 09:50:00", "output": {"class": "TradeEntry", "data": {"trade_id": "d297e798", "full_trade_id": "d297e798-5468-4695-8b3e-b0c8f417c067", "signal": "Support1Entry", "entry_time": "09:50:00.000", "entry_price": 6380.75, "contracts": 5, "stop_price": 6374.5, "actual_risk": 143.75, "stop_distance": 5.75, "support_level": 6378.0, "breakdown_low": 6375.5}}}, {"input": "[TRADE SUMMARY] Entry:6380.75 | Exit:6387.75 | PnL:$175.00(+7.00pts_per) | R:1.12 | MFE:$187.50 | Capture:93.3% | Duration:3min | Reason:Runner_Trail", "output": {"class": "TradeSummary", "data": {"entry_price": 6380.75, "exit_price": 6387.75, "total_pnl": 175.0, "points_per_contract": 7.0, "r_multiple": 1.12, "mfe": 187.5, "mfe_capture_percent": 93.3, "duration_minutes": 3, "exit_reason": "Runner_Trail"}}}, {"input": "09:50:00.000 [MFE] [trade=d297e798] TRAIL ARMED | Reason: THRESHOLD+PROFIT+POINTS | MFE: $187.50 | Thresh: $188 | Min$: $100 | MinPts: 2.0 | FavorablePts: 7.50 | (OriginalRisk: $156 x 1.2) | Contracts: 5 | NativeStopsCancelled: true | TradeID: d297e798-5468-4695-8b3e-b0c8f417c067 | Time: 09:50:00", "output": {"class": "MFETrailEvent", "data": {"trade_id": "d297e798", "event_type": "ARMED", "mfe_amount": 187.5, "threshold": 188.0, "is_armed": true, "trail_reason": "THRESHOLD+PROFIT+POINTS", "timestamp": "09:50:00.000"}}}]}}