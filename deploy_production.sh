#!/bin/bash
# ManciniMES Analytics Pipeline - Production Deployment
# One-click deployment with comprehensive error handling and validation

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load configuration from file if available
if [ -f "pipeline_config.json" ]; then
    echo -e "${BLUE}📋 Loading configuration from pipeline_config.json${NC}"
    PROJECT_ID=$(jq -r '.deployment.project_id // empty' pipeline_config.json)
    REGION=$(jq -r '.deployment.region // "us-central1"' pipeline_config.json)
    SERVICE_NAME=$(jq -r '.deployment.service_name // "mes-log-extract"' pipeline_config.json)
    DATASET_ID=$(jq -r '.deployment.dataset_id // "mes_mancini"' pipeline_config.json)
    LANGEXTRACT_ENDPOINT=$(jq -r '.langextract.endpoint // "https://api.langextract.com"' pipeline_config.json)
    ALERT_EMAIL=$(jq -r '.monitoring.alert_email // empty' pipeline_config.json)
else
    echo -e "${YELLOW}⚠️  No pipeline_config.json found, using defaults${NC}"
fi

# Configuration with defaults and overrides
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-${PROJECT_ID:-$(gcloud config get-value project)}}
REGION=${REGION:-"us-central1"}
SERVICE_NAME=${SERVICE_NAME:-"mes-log-extract"}
DATASET_ID=${DATASET_ID:-"mes_mancini"}
LANGEXTRACT_ENDPOINT=${LANGEXTRACT_ENDPOINT:-"https://api.langextract.com"}

echo -e "${BLUE}🚀 ManciniMES Analytics Pipeline - Production Deployment${NC}"
echo -e "${BLUE}=================================================${NC}"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Dataset: $DATASET_ID"
echo ""

# Validation function
validate_prerequisites() {
    echo -e "${YELLOW}🔍 Validating prerequisites...${NC}"
    
    # Check gcloud auth
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo -e "${RED}❌ No active gcloud authentication found${NC}"
        echo "Run: gcloud auth login"
        exit 1
    fi
    
    # Check project ID
    if [ -z "$PROJECT_ID" ]; then
        echo -e "${RED}❌ PROJECT_ID not set${NC}"
        echo "Set GOOGLE_CLOUD_PROJECT environment variable or run: gcloud config set project YOUR_PROJECT_ID"
        exit 1
    fi
    
    # Check required files
    local required_files=("langextract_spec.json" "bigquery_ddl.sql" "bigquery_session_ddl.sql" "cloud_run_handler.py")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}❌ Required file missing: $file${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✅ Prerequisites validated${NC}"
}

# Enable required APIs
enable_apis() {
    echo -e "${YELLOW}🔧 Enabling required APIs...${NC}"
    
    local apis=(
        "run.googleapis.com"
        "bigquery.googleapis.com" 
        "eventarc.googleapis.com"
        "storage.googleapis.com"
        "scheduler.googleapis.com"
        "logging.googleapis.com"
        "monitoring.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        echo "Enabling $api..."
        gcloud services enable "$api" --quiet
    done
    
    echo -e "${GREEN}✅ APIs enabled${NC}"
}

# Setup BigQuery
setup_bigquery() {
    echo -e "${YELLOW}📊 Setting up BigQuery dataset and tables...${NC}"
    
    # Create dataset
    if ! bq ls -d "$PROJECT_ID:$DATASET_ID" >/dev/null 2>&1; then
        bq mk --location=US --description="ManciniMES Trading Analytics" "$PROJECT_ID:$DATASET_ID"
        echo "Created dataset: $DATASET_ID"
    else
        echo "Dataset already exists: $DATASET_ID"
    fi
    
    # Run main DDL
    echo "Creating main tables and views..."
    bq query --use_legacy_sql=false < bigquery_ddl.sql
    
    # Run session continuity DDL
    echo "Creating session continuity tables..."
    bq query --use_legacy_sql=false < bigquery_session_ddl.sql
    
    echo -e "${GREEN}✅ BigQuery setup complete${NC}"
}

# Build and deploy Cloud Run
deploy_cloud_run() {
    echo -e "${YELLOW}🐳 Building and deploying Cloud Run service...${NC}"
    
    # Create optimized Dockerfile
    cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY cloud_run_handler.py .
COPY langextract_spec.json .
COPY langextract_config.py .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# Environment variables
ENV PORT=8080
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# Run the application
CMD exec gunicorn --bind :$PORT --workers 2 --threads 4 --timeout 300 \
    --worker-class gthread --worker-connections 1000 \
    --access-logfile - --error-logfile - \
    cloud_run_handler:app
EOF

    # Create optimized requirements.txt
    cat > requirements.txt << 'EOF'
flask==3.0.0
google-cloud-bigquery==3.13.0
google-cloud-storage==2.10.0
google-cloud-secret-manager==2.16.4
requests==2.31.0
gunicorn==21.2.0
cryptography==41.0.7
EOF

    # Build with Cloud Build for better performance
    echo "Building container image..."
    gcloud builds submit --tag "gcr.io/$PROJECT_ID/$SERVICE_NAME" --timeout=600s
    
    # Create Secret Manager secret for API key
    echo "Setting up Secret Manager..."
    if ! gcloud secrets describe langextract-api-key >/dev/null 2>&1; then
        echo "Please enter your LangExtract API key:"
        read -s api_key
        echo "$api_key" | gcloud secrets create langextract-api-key --data-file=-
    fi

    # Deploy to Cloud Run with production settings
    echo "Deploying to Cloud Run..."
    gcloud run deploy "$SERVICE_NAME" \
        --image "gcr.io/$PROJECT_ID/$SERVICE_NAME" \
        --platform managed \
        --region "$REGION" \
        --allow-unauthenticated \
        --memory 4Gi \
        --cpu 2 \
        --timeout 900 \
        --concurrency 10 \
        --max-instances 20 \
        --min-instances 0 \
        --service-account "mes-pipeline-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
        --set-env-vars="GOOGLE_CLOUD_PROJECT=$PROJECT_ID,LANGEXTRACT_ENDPOINT=$LANGEXTRACT_ENDPOINT,LANGEXTRACT_PROVIDER=gemini" \
        --set-secrets="LANGEXTRACT_API_KEY=langextract-api-key:latest" \
        --execution-environment gen2 \
        --cpu-boost \
        --quiet
    
    echo -e "${GREEN}✅ Cloud Run service deployed${NC}"
}

# Setup service account and IAM
setup_iam() {
    echo -e "${YELLOW}🔐 Setting up service account and IAM...${NC}"
    
    local sa_name="mes-pipeline-sa"
    local sa_email="$sa_name@$PROJECT_ID.iam.gserviceaccount.com"
    
    # Create service account
    if ! gcloud iam service-accounts describe "$sa_email" >/dev/null 2>&1; then
        gcloud iam service-accounts create "$sa_name" \
            --display-name="ManciniMES Pipeline Service Account" \
            --description="Service account for ManciniMES analytics pipeline"
    fi
    
    # Grant necessary permissions (least privilege)
    local project_roles=(
        "roles/storage.objectViewer"
        "roles/run.invoker"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/secretmanager.secretAccessor"
    )

    for role in "${project_roles[@]}"; do
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$sa_email" \
            --role="$role" \
            --quiet
    done

    # Grant dataset-scoped BigQuery permissions
    bq add-iam-policy-binding \
        --member="serviceAccount:$sa_email" \
        --role="roles/bigquery.dataEditor" \
        "$PROJECT_ID:$DATASET_ID"

    bq add-iam-policy-binding \
        --member="serviceAccount:$sa_email" \
        --role="roles/bigquery.jobUser" \
        "$PROJECT_ID:$DATASET_ID"
    
    echo -e "${GREEN}✅ IAM setup complete${NC}"
}

# Setup monitoring and alerting
setup_monitoring() {
    echo -e "${YELLOW}📊 Setting up monitoring and alerting...${NC}"
    
    # Create log-based metrics
    gcloud logging metrics create mes_processing_errors \
        --description="ManciniMES log processing errors" \
        --log-filter="resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND severity>=ERROR" \
        --quiet || echo "Metric already exists"
    
    gcloud logging metrics create mes_processing_success \
        --description="ManciniMES successful processing events" \
        --log-filter="resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND \"Processing completed successfully\"" \
        --quiet || echo "Metric already exists"

    # Setup notification channel (email)
    echo "Setting up notification channel..."

    # Use config file email or prompt
    if [ -z "$ALERT_EMAIL" ]; then
        echo "Please enter your email for alerts:"
        read -p "Email: " alert_email
    else
        alert_email="$ALERT_EMAIL"
        echo "Using email from config: $alert_email"
    fi

    if [ -n "$alert_email" ]; then
        # Try to create notification channel (fallback to manual if alpha commands fail)
        if gcloud alpha monitoring channels create --help >/dev/null 2>&1; then
            cat > notification_channel.json << EOF
{
  "type": "email",
  "displayName": "ManciniMES Alerts",
  "description": "Email notifications for ManciniMES pipeline",
  "labels": {
    "email_address": "$alert_email"
  },
  "enabled": true
}
EOF

            local channel_id
            if channel_id=$(gcloud alpha monitoring channels create --channel-content-from-file=notification_channel.json --format="value(name)" 2>/dev/null); then
                # Create alerting policy with notification
                cat > alerting_policy.json << EOF
{
  "displayName": "ManciniMES Processing Errors",
  "conditions": [
    {
      "displayName": "Log processing errors",
      "conditionThreshold": {
        "filter": "metric.type=\"logging.googleapis.com/user/mes_processing_errors\"",
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 0,
        "duration": "300s"
      }
    }
  ],
  "notificationChannels": ["$channel_id"],
  "alertStrategy": {
    "autoClose": "1800s"
  },
  "enabled": true
}
EOF

                gcloud alpha monitoring policies create --policy-from-file=alerting_policy.json 2>/dev/null || echo "Alert policy creation failed - set up manually in console"
                rm -f notification_channel.json alerting_policy.json
            else
                echo -e "${YELLOW}⚠️  Alpha monitoring commands failed - please set up notification channels manually in Cloud Console${NC}"
                echo "   Go to: https://console.cloud.google.com/monitoring/alerting"
            fi
        else
            echo -e "${YELLOW}⚠️  Alpha monitoring commands not available - please set up notification channels manually in Cloud Console${NC}"
            echo "   Go to: https://console.cloud.google.com/monitoring/alerting"
        fi
    fi

    echo -e "${GREEN}✅ Monitoring setup complete${NC}"
}

# Setup Cloud Scheduler
setup_scheduler() {
    echo -e "${YELLOW}⏰ Setting up Cloud Scheduler...${NC}"
    
    local service_url
    service_url=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)")
    
    # EOD finalization job
    gcloud scheduler jobs create http mes-eod-finalizer \
        --location="$REGION" \
        --schedule="0 21 * * 1-5" \
        --time-zone="America/New_York" \
        --uri="$service_url/finalize-day" \
        --http-method=POST \
        --headers="Content-Type=application/json" \
        --message-body='{"action": "finalize_day"}' \
        --quiet || echo "Scheduler job already exists"
    
    echo -e "${GREEN}✅ Scheduler setup complete${NC}"
}

# Test deployment
test_deployment() {
    echo -e "${YELLOW}🧪 Testing deployment...${NC}"
    
    local service_url
    service_url=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)")
    
    # Test health endpoint
    if curl -f -s "$service_url/health" >/dev/null; then
        echo -e "${GREEN}✅ Health check passed${NC}"
    else
        echo -e "${RED}❌ Health check failed${NC}"
        return 1
    fi
    
    # Test BigQuery connectivity
    if bq query --use_legacy_sql=false "SELECT COUNT(*) FROM \`$PROJECT_ID.$DATASET_ID.session_status\`" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ BigQuery connectivity verified${NC}"
    else
        echo -e "${RED}❌ BigQuery connectivity failed${NC}"
        return 1
    fi
}

# Cleanup function
cleanup() {
    echo "Cleaning up temporary files..."
    rm -f Dockerfile requirements.txt
}

# Main execution
main() {
    trap cleanup EXIT
    
    validate_prerequisites
    enable_apis
    setup_bigquery
    setup_iam
    deploy_cloud_run
    setup_monitoring
    setup_scheduler
    test_deployment
    
    echo ""
    echo -e "${GREEN}🎉 DEPLOYMENT COMPLETE!${NC}"
    echo -e "${GREEN}========================${NC}"
    echo ""
    
    local service_url
    service_url=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.url)")
    
    echo -e "${BLUE}📊 BigQuery Dataset:${NC} $PROJECT_ID.$DATASET_ID"
    echo -e "${BLUE}🚀 Cloud Run Service:${NC} $service_url"
    echo -e "${BLUE}📈 Monitoring:${NC} https://console.cloud.google.com/monitoring"
    echo ""
    echo -e "${YELLOW}🎯 NEXT STEPS:${NC}"
    echo "1. Set LANGEXTRACT_API_KEY in Cloud Run environment variables"
    echo "2. Configure Google Drive sync for your NT8 logs folder"
    echo "3. Schedule the PowerShell script to run daily at 3:55 PM ET"
    echo "4. Create Looker Studio dashboard connected to BigQuery"
    echo "5. Test with a sample log folder"
    echo ""
    echo -e "${GREEN}🏆 Your automated trading analytics pipeline is ready!${NC}"
}

# Run main function
main "$@"
